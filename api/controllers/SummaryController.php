<?php

namespace api\controllers;

use common\services\AutoSummaryService;
use common\models\NewsSubdomain;
use common\models\Poll;
use common\models\PollTag;
use Yii;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\rest\Controller;
use yii\web\Response;

/**
 * Summary API Controller for auto-generating content summaries
 * Integrates with Swagger API for AI-powered summary generation
 */
class SummaryController extends Controller
{
    private static $_autoSummaryService = null;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        $behaviors['contentNegotiator'] = [
            'class' => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];

        $behaviors['verbFilter'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'generate' => ['POST'],
            ],
        ];

        return $behaviors;
    }

    public function actionGenerate()
    {
        $request = Yii::$app->request;

        // Validate required parameters
        $title = $request->post('title');
        $content = $request->post('content');

        if (empty($title) || empty($content)) {
            return [
                'success' => false,
                'error' => 'Title and content are required parameters',
                'code' => 400
            ];
        }

        try {
            // Generate summary using external Swagger API
            $summaryData = $this->generateSummaryFromSwaggerAPI($title, $content);

            if ($summaryData) {
                return [
                    'success' => true,
                    'data' => $summaryData,
                    'message' => 'Summary generated successfully'
                ];
            } else {
                // Return fallback summary if API fails
                $fallbackData = $this->generateFallbackSummary($title, $content);
                return [
                    'success' => true,
                    'data' => $fallbackData,
                    'message' => 'Summary generated using fallback method'
                ];
            }
        } catch (\Exception $e) {
            Yii::error('Summary generation failed: ' . $e->getMessage(), __METHOD__);

            return [
                'success' => false,
                'error' => 'Failed to generate summary: ' . $e->getMessage(),
                'code' => 500
            ];
        }
    }

    /**
     * Generate summary using external Swagger API
     *
     * @param string $title
     * @param string $content
     * @return array|null
     */
    private function generateSummaryFromSwaggerAPI($title, $content)
    {
        // Swagger API endpoint for AI summary generation
        $url = 'https://api.swagger.io/v1/ai-summary';

        // Input data for the API
        $data = [
            'title' => $title,
            'content' => self::getService()->cleanContentForSummary($content), // Clean content thoroughly
            'format' => 'structured_json'
        ];

        // Initialize cURL session
        $ch = curl_init($url);

        // Set cURL options (without authorization for now)
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json',
                'User-Agent: GMU-Backend/1.0'
            ],
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Handle cURL errors
        if ($error) {
            Yii::error('cURL Error in generateSummaryFromSwaggerAPI: ' . $error, __METHOD__);
            return null;
        }

        // Handle HTTP errors
        if ($httpCode !== 200) {
            Yii::error("HTTP Error {$httpCode} in generateSummaryFromSwaggerAPI: " . $response, __METHOD__);
            return null;
        }

        $responseObj = json_decode($response, true);

        // Validate response structure
        if (!$responseObj || !isset($responseObj['header'])) {
            Yii::error('Invalid response structure in generateSummaryFromSwaggerAPI: ' . $response, __METHOD__);
            return null;
        }

        // Ensure status field is present (default to active if not provided by API)
        if (!isset($responseObj['status'])) {
            $responseObj['status'] = 1;
        }

        return $responseObj;
    }

    /**
     * Generate a fallback summary when API fails
     *
     * @param string $title
     * @param string $content
     * @return array
     */
    private function generateFallbackSummary($title, $content)
    {
        // Clean content more thoroughly
        $cleanContent = self::getService()->cleanContentForSummary($content);

        // Generate a proper excerpt (first few sentences)
        $excerpt = self::getService()->generateExcerpt($cleanContent, $title);

        return [
            'header' => $title,
            'paragraphSummary' => $excerpt,
            'status' => 1, // Default active status
            'quickFacts' => [
                'Content available for detailed reading',
                'Check official sources for latest updates',
                'Information is regularly maintained and updated',
                'Content provides comprehensive coverage',
                'Reliable source for accurate information'
            ],
            'keyContent' => [
                'Title: ' . $title,
                'Content length: ' . strlen($cleanContent) . ' characters',
                'Comprehensive information coverage',
                'Well-structured and organized content',
                'Relevant and up-to-date details'
            ],
            'faqs' => [
                [
                    'question' => 'What is this content about?',
                    'answer' => $excerpt
                ],
                [
                    'question' => 'Who is the target audience?',
                    'answer' => 'This content is designed for readers seeking comprehensive information on the topic.'
                ],
                [
                    'question' => 'How current is this information?',
                    'answer' => 'The information is regularly updated to ensure accuracy and relevance.'
                ],
                [
                    'question' => 'Where can I find more details?',
                    'answer' => 'Additional information can be found in the main content sections and related resources.'
                ]
            ],
            'quiz' => [
                [
                    'question' => 'What is the main topic?',
                    'options' => ['General Information', 'Educational Content', 'Update'],
                    'answer' => 'Educational Content'
                ],
                [
                    'question' => 'How is the content structured?',
                    'options' => ['Randomly', 'Systematically', 'Chronologically'],
                    'answer' => 'Systematically'
                ],
                [
                    'question' => 'What type of information is provided?',
                    'options' => ['Basic overview', 'Detailed analysis', 'Quick summary'],
                    'answer' => 'Detailed analysis'
                ],
                [
                    'question' => 'Is this content reliable?',
                    'options' => ['Yes', 'No', 'Partially'],
                    'answer' => 'Yes'
                ]
            ],
            'poll' => [
                [
                    'question' => 'How helpful is this information?',
                    'options' => ['Very Helpful', 'Somewhat Helpful', 'Not Helpful', 'Extremely Useful', 'Moderately Useful']
                ]
            ]
        ];
    }

    /**
     * Public method to generate summary (can be called from other parts of the application)
     *
     * @param string $title
     * @param string $content
     * @param int $entityId
     * @return array|null
     */
    public static function generateSummary($title, $content, $entityId, $entity, $langCode = 1) //1 en, 2 hi
    {
        // Try Swagger API first
        $result = self::generateSummaryFromSwaggerAPI($title, $content);

        // If API fails, use fallback
        if (!$result) {
            $result = self::generateFallbackSummary($title, $content);
        }

        // If Id is provided, save to database tables
        if ($entityId && $result && is_array($result)) {
            // Determine entity type and language code from request or default to '' and 'en'
            $entity = Yii::$app->request->post('entity', $entity);
            $langCode = Yii::$app->request->post('lang_code', $langCode);
            self::getService()->saveSummaryToTables($result, $entityId, $entity, $langCode);
        }

        return $result;
    }

    private static function getService()
    {
        if (!self::$_autoSummaryService) {
            self::$_autoSummaryService = new AutoSummaryService();
        }
        return self::$_autoSummaryService;
    }
}
