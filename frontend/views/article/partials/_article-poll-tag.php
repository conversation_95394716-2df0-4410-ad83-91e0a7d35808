<?php

use frontend\helpers\Html;

if (!empty($articlePoll->poll)) {
    $question = !empty($articlePoll->poll->question) ? $articlePoll->poll->question : '';
    $options = !empty($articlePoll->poll->options) ? json_decode($articlePoll->poll->options) : '';
    $poll_id = !empty($articlePoll->poll->id) ? $articlePoll->poll->id : '';
    $entity_id = !empty($articlePoll->entity_id) ? $articlePoll->entity_id : '';
}
$totalVotes = 0;
if (!empty($options) && !empty($articlePollPercentage)):
    foreach ($articlePollPercentage as $row) {
        $totalVotes += (int)$row['total'];
    }
endif;
?>

<div id="article-poll" class="poll-body">
    <div class="poll-wrapper">
        <div class="poll-header">
            <h3>Student Buzz Right Now</h3>
            <p>Take this quick poll – we’re all ears.</p>
        </div>
        <div class="poll-container-1"></div>
        <div class="poll-container-2"></div>
        <div class="poll-container">
            <div class="question-title">Question</div>
            <div class="border-bottom"></div>
            <div class="question-text"><?= !empty($question) ? $question : ''; ?></div>
            <?php if (!empty($options)): ?>
                <div class="options <?php if ($userArticlePoll !== null && $userArticlePoll !== ''):
                    ?>disabled<?php
                                    endif; ?>">
                    <?php foreach ($options as $key => $option): ?>
                        <div class="option <?php if (($userArticlePoll !== null && $userArticlePoll !== '') && $userArticlePoll == $key):
                            ?>selected<?php
                                           endif; ?>"
                            data-key="<?= $key; ?>">
                            <?= $option; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            <div class="buttons">
                <button class="poll-response-submit articleLeadValue" data-entityid="<?= $entity_id; ?>" data-entity="article" disabled>
                    SUBMIT
                </button>
                <button class="see-others-btn articleLeadValue" <?= (($totalVotes <= 0) && !(Yii::$app->user->isGuest)) ? 'disabled' : '' ?> data-entityid="<?= $entity_id; ?>" data-entity="article">
                    See what others are Saying
                </button>
            </div>

            <?php if (!empty($articlePollVotes) && ($articlePollVotes > 4)): ?>
                <div class="live-votes">
                    <span>
                        <img src="../../../yas/images/live-gif-small.gif" loading="lazy" class="lazyload gifLive" width="47" height="20" alt="Live Gif">
                    </span>
                    <?= $articlePollVotes . ' Students voted'; ?>
                </div>
            <?php endif; ?>
            <?= Html::hiddenInput('poll_id', $poll_id) ?>
            <?= Html::hiddenInput('entity_id', $entity_id) ?>
            <?= Html::hiddenInput('user_id', Yii::$app->user->isGuest ? null : Yii::$app->user->id) ?>
        </div>
    </div>
</div>

<!-- Thank You Modal -->
<div id="thankYouModal" class="custom-modal" style="display:none;">
    <div class="custom-modal-content">
        <span class="close-modal">
            <img height="45" width="45" class="tick-icon" src="../../../yas/images/closeModal.png" loading="lazy" class="lazyload" alt="close">
        </span>
        <h2>Thank You, Your Response has been submitted</h2>
        <p>Sign up to see what others are saying!</p>
        <button class="see-others-btn" <?= $totalVotes <= 0 ? 'disabled' : '' ?>>
            See what others are Saying
        </button>
        <p class="small-text" style="padding-top: 5px;"><b>Join 50,000+ students</b> making smarter college choices</p>
    </div>
</div>

<!-- Modal Overlay -->
<div id="trending-poll-overlay"></div>

<!-- Trending poll Modal -->
<div id="trending-poll" class="trending-poll poll-container">
    <div class="poll-header">
        <h3>Student Buzz Right Now</h3>
        <p>Take this quick poll – we’re all ears.</p>
        <span class="close-trending-poll">&times;</span>
    </div>
    <div class="poll-container-1"></div>
    <div class="poll-container-2"></div>
    <div class="poll-container">
        <div class="question-title">Question</div>
        <div class="border-bottom"></div>
        <div class="question-text"><?= !empty($question) ? $question : ''; ?></div>
        <?php if (!empty($options)): ?>
            <div class="options">
                <?php
                $high_percent = 0;
                foreach ($options as $key => $option) {
                    $votes = !empty($articlePollPercentage[$key]) ? (int)$articlePollPercentage[$key]['total'] : 0;
                    $percentage = $totalVotes > 0 ? round(($votes / $totalVotes) * 100) : 0;
                    $high_percent = max($high_percent, $percentage);
                }
                $percentages = [];
                $totalVotes = $totalVotes > 0 ? $totalVotes : 1; // prevent division by zero
                $sum = 0;

                // First, calculate floored percentages
                foreach ($options as $key => $option) {
                    $votes = !empty($articlePollPercentage[$key]) ? (int)$articlePollPercentage[$key]['total'] : 0;
                    $percentage = floor(($votes / $totalVotes) * 100);
                    $percentages[$key] = $percentage;
                    $sum += $percentage;
                }

                // Distribute remaining % (to fix rounding errors)
                $remaining = 100 - $sum;
                if ($remaining > 0) {
                    foreach ($options as $key => $option) {
                        if ($remaining <= 0) {
                            break;
                        }
                        $percentages[$key] += 1;
                        $remaining--;
                    }
                }

                foreach ($options as $key => $option): ?>
                    <?php
                    $percentage = $percentages[$key];
                    $progressClass = (($percentage == max($percentages)) && ($sum !== 0.0)) ? ' highest' : '';
                    ?>
                    <div class="option <?= $progressClass; ?>">
                        <div class="progress-bar <?= $progressClass; ?>" style="width: <?= ($sum !== 0.0) ? $percentage : 0; ?>%;">
                            <img class="tick-icon" src="../../../yas/images/check-circle-solid.png" loading="lazy" alt="checked" style="display: none;">
                        </div>
                        <div class="option-txt">
                            <?= $option; ?>
                            <div class="percentage">
                                <?= ($sum !== 0.0) ? $percentage : 0; ?>%
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

            </div>
        <?php endif; ?>
        <?php if (!empty($articlePollVotes) && ($articlePollVotes > 4)): ?>
            <div class="live-votes">
                <span>
                    <img src="../../../yas/images/live-gif-small.gif" loading="lazy" class="lazyload gifLive" width="47" height="20" alt="Live Gif">
                </span>
                <?= $articlePollVotes . ' Students voted'; ?>
            </div>
        <?php endif; ?>
    </div>
</div>