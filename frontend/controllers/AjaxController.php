<?php

namespace frontend\controllers;

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use common\models\Article;
use common\models\Board;
use common\models\BoardContent;
use common\models\Category;
use common\models\Comment;
use common\models\Lead;
use common\models\old\GmuSaLeads;
use common\models\Review;
use common\models\StudentOtp;
use common\services\CollegeService;
use common\services\NewsService;
use common\services\SmsService;
use common\services\SponsorService;
use common\services\LeadService;
use common\services\S3Service;
use frontend\helpers\Util;
use frontend\helpers\Url;
use frontend\models\CommentForm;
use frontend\models\Student;
use frontend\models\WpCommentForm;
use common\models\College as CollegeSQL;
use common\models\Course;
use frontend\services\AjaxService;
use Yii;
use yii\db\Query;
use yii\helpers\Json;
use yii\web\Response;
use yii\widgets\ActiveForm;
use common\services\ReviewService;
use common\services\StudentService;
use yii\web\NotFoundHttpException;
use common\models\City;
use common\models\Career;
use common\models\CollegeContent;
use common\models\CollegeCourse;
use common\models\CollegeCourseMapping;
use common\models\CollegeProgram;
use common\models\CollegeProgramExam;
use common\models\CourseContent;
use common\models\Degree;
use common\models\Exam;
use common\models\ExamContent;
use common\models\LeadBucketCtaDetail;
use common\models\NewsSubdomain;
use common\models\Program;
use common\models\ProgramCourseMapping;
use common\models\SpecializationNew;
use common\models\State;
use common\models\StudentCollegeShortlist;
use common\models\Stream;
use common\models\MediaDrive;
use common\models\MediaDriveCtaTypeMapping;
use common\models\MediaDriveUploadType;
use common\models\NewsContentSubdomain;
use yii\helpers\StringHelper;
use common\models\QnaAnswer;
use common\models\SaCity;
use common\services\UserService;
use frontend\helpers\Html;
use common\models\SaCollege;
use common\models\SaCountry;
use common\models\SaCourse;
use common\models\SaDegree;
use common\models\SaSpecialization;
use common\models\SaStream;
use common\services\StudyAbroadService;
use frontend\services\ExamService;
use Mpdf\Mpdf;
use ZipArchive;
use common\models\ArticleSubpage;
use common\models\ArticleSubpageSection;
use common\models\ArticleSubpageSubsectionSubtopic;
use common\models\ArticleSubpageSubsectionQuesAns;
use common\models\ArticleSubpageSubsection;
use common\models\CutOff;
use common\models\CutoffCategory;
use common\models\documents\College;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;
use common\models\PollResponse;
use common\models\UserAttempts;
use common\models\QuestionMetrics;
use common\services\ClpService;
use common\services\PopularCollegeService;
use frontend\services\ArticleService;
use yii\helpers\ArrayHelper;

class AjaxController extends \yii\web\Controller
{
    protected $ajaxService;

    protected $newsService;
    protected $collegeService;
    protected $leadService;
    protected $articleService;


    public function __construct(
        $id,
        $module,
        AjaxService $ajaxService,
        CollegeService $collegeService,
        NewsService $newsService,
        LeadService $leadService,
        ArticleService $articleService,
        $config = []
    ) {
        $this->ajaxService = $ajaxService;
        $this->newsService = $newsService;
        $this->leadService = $leadService;
        $this->collegeService = $collegeService;
        $this->articleService = $articleService;
        parent::__construct($id, $module, $config);
    }

    public function actionGetColleges()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        return Json::encode($this->ajaxService->getColleges($request->get('q')));
    }

    public function actionGetExams()
    {
        $request = \Yii::$app->request;

        \Yii::$app->response->format = Response::FORMAT_JSON;

        return Json::encode($this->ajaxService->getExams($request->get('q')));
    }

    public function actionGetCourses()
    {
        $request = \Yii::$app->request;

        \Yii::$app->response->format = Response::FORMAT_JSON;

        return Json::encode($this->ajaxService->getCourses($request->get('q')));
    }

    public function actionGetBoards()
    {
        $request = \Yii::$app->request;

        \Yii::$app->response->format = Response::FORMAT_JSON;

        return Json::encode($this->ajaxService->getBoards($request->get('q')));
    }

    public function actionSearch($type, $q = null)
    {

        Yii::$app->response->format = Response::FORMAT_JSON;

        $response = [];

        switch ($type) {
            case 'college':
                $response = $this->ajaxService->getColleges($q);
                break;
            case 'college-review':
                $response = $this->ajaxService->getReviewColleges($q);
                break;
            case 'exam':
                $response = $this->ajaxService->getExams($q);
                break;
            case 'course':
                $response = $this->ajaxService->getCourses($q);
                break;
            case 'board':
                $response = $this->ajaxService->getBoards($q);
        }

        return Json::encode($response);
    }

    public function actionSearchReviewCollege($type, $q = null)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $response = [];

        $response = $this->ajaxService->getReviewColleges($q);

        return Json::encode($response);
    }

    public function actionComment()
    {
        $model = new CommentForm();

        if (Yii::$app->request->post('Comment')) {
            $model = new Comment();
        }

        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return ['success' => true, 'model' => $model->attributes];
        } else {
            return ['success' => false, 'errors' => ActiveForm::validate($model)];
        }
    }

    public function actionVerifyOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();
        $otp = implode('', $request['digit']);

        if (strlen($otp) < 4) {
            return ['success' => false, 'message' => 'Otp Field required'];
        }

        $model = Lead::find()
            ->where(['mobile' => Util::cleanMobile($request['LeadForm']['mobile'])])
            ->andWhere(['otp' => (int) $otp])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        if (!$model) {
            return ['success' => false, 'message' => 'Please enter the valid otp'];
        }

        //Student Auto Login Implementation
        $student = Student::findByPhone($request['LeadForm']['mobile']);
        StudentService::updateStudent($request, $model, $student ? 'sign-in' : 'verify-otp');
        //Student Auto Login Implementation

        $model->is_mobile_verified = Lead::MOBILE_VERIFIED_YES;
        $model->save();

        $entity = $model->entity;

        if ($model->cta_location == 'auto-popup' || $model->cta_location == 'all-colleges') {
            $entity = '';
        }
        $is_sponsorCollege = false;
        if (isset($request['LeadForm']['entity_id'])) {
            if ($entity == CollegeSQL::ENTITY_COLLEGE) {
                $is_sponsorCollege = CollegeSQL::find()
                    ->where(['id' => $request['LeadForm']['entity_id'], 'is_sponsored' => 1])
                    ->exists();
            }
        }
        $sponsorColleges = isset($request->sponsorColleges) ? true : false;
        $interest_state_id = City::find()->select('state_id')->where(['id' => $request['LeadForm']['current_location']])->one()->state_id ?? 0;

        $state_name = '';
        $state_old_id = '';
        if ($interest_state_id != 0) {
            $user_state_id = State::find()->select(['old_id', 'name'])->where(['id' => $interest_state_id])->one();
            $state_name = $user_state_id->name;
            $state_old_id = (string)$user_state_id->old_id;
        }
        $student = Student::findByPhone($request['LeadForm']['mobile']);
        if (!empty($student)) {
            Yii::$app->user->login($student, 3600 * 24 * 30);
        }

        return [
            'success' => true,
            'entity' => $entity ?? '',
            'model' => $sponsorColleges,
            'message' => 'Thank you for submitting the form.',
            'mobile' => !empty($request['LeadForm']) && isset($request['LeadForm']['mobile']) ? $request['LeadForm']['mobile'] : '',
            'email' => !empty($request['LeadForm']) && isset($request['LeadForm']['email']) ? $request['LeadForm']['email'] : '',
            'name' => !empty($request['LeadForm']) && isset($request['LeadForm']['name']) ? $request['LeadForm']['name'] : '',
            'interested_course' => !empty($request['LeadForm']) && isset($request['LeadForm']['interested_course']) ? $request['LeadForm']['interested_course'] : '',
            'qualification' => !empty($request['LeadForm']) && isset($request['LeadForm']['qualification']) ? $request['LeadForm']['qualification'] : '',
            'current_location' => !empty($request['LeadForm']) && isset($request['LeadForm']['current_location']) ? $request['LeadForm']['current_location'] : '',
            'cta_location' => !empty($request['LeadForm']) && isset($request['LeadForm']['cta_location']) ? $request['LeadForm']['cta_location'] : '',
            'is_sponsorCollege' => $is_sponsorCollege,
            'state_name' => $state_name ?? '',
            'state_id' =>  $state_old_id ?? 0,
            'csrf' => Yii::$app->request->getCsrfToken(),
            'source_url' => isset($request['LeadForm']['url']) ? $request['LeadForm']['url'] : '',
            'source' =>  Lead::SOURCE_ORGANIC,
            'user_status' => Lead::LEAD_USER_STATUS,
            'user_type' => Lead::STUDENT_TYPE_LEAD,
        ];
    }

    public function actionResendOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();
        $model = Lead::find()->where(['mobile' => $request['LeadForm']['mobile']])->orderBy(['id' => SORT_DESC])->one();

        if (!$model) {
            return ['success' => false];
        }
        $model->otp = rand(1000, 9999);

        if ($model->save()) {
            SmsService::sendOtp($model->mobile, $model->otp);

            return ['success' => true];
        }

        return ['success' => false];
    }

    public function actionWpComment()
    {
        $model = new WpCommentForm();
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        if ($model->load($request)) {
            $response = $this->newsService->saveComment($request);

            if (isset($response->status) && $response->status == 201) {
                return ['status' => true, 'message' => $response->message];
            } else {
                return [
                    'status' => false,
                    'message' => isset($response->message) ? ContentHelper::htmlDecode($response->message) : 'Something went wrong. Please try again later',
                ];
            }
        } else {
            return ['status' => false, 'message' => 'Internal server error'];
        }
    }

    public function actionPrimaryNavigation()
    {
        return $this->renderPartial('/layouts/_header');
    }

    public function actionCollegeReviews()
    {
        $data = [];
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();
        $reviews = (new ReviewService)->getCollegeReviews($request['id'], $limit = 10, $request['page']);

        $data['review'] = $this->renderPartial('/college/partials/_review-sub-card', [
            'reviews' => $reviews ?? [],
        ]);
        $data['pageNo'] = $request['page'] + 10;

        return $data;
    }
    //sa-lead

    public function actionSaVerifyOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();

        $otp = implode('', $request['digit']);

        if (strlen($otp) < 4) {
            return ['success' => false, 'message' => 'Otp Field required'];
        }

        $model = GmuSaLeads::find()
            ->where(['mobile_num' => Util::cleanMobile($request['GmuSaLeads']['mobile_num'])])
            ->orderBy(['id' => SORT_DESC])
            ->one();

        if (!$model) {
            return ['success' => false, 'message' => 'Please enter the valid otp'];
        }

        $model->save();

        return ['success' => true, 'message' => 'Thank you for submitting the form.'];
    }

    public function actionSaResendOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();
        $model = GmuSaLeads::find()->where(['mobile_num' => $request['GmuSaLeads']['mobile_num']])->orderBy(['id' => SORT_DESC])->one();

        if (!$model) {
            return ['success' => false];
        }
        $otp = rand(1000, 9999);

        if ($model->save()) {
            SmsService::sendOtp($model->mobile_num, $otp);

            return ['success' => true];
        }

        return ['success' => false];
    }

    public function actionSaCity()
    {
        $request = Yii::$app->request->post();
        Yii::$app->response->format = Response::FORMAT_JSON;

        $country = $request['current_country'];

        $query = new Query();
        $query->select('id')
            ->from('gmu_sa_current_country')
            ->where(['current_country' => $country]);

        $countryId = $query->one(\Yii::$app->gmudb);

        $cityQuery = new Query();
        $cityQuery->select('city')
            ->from('gmu_sa_current_city')
            ->where(['current_country_id' => $countryId]);

        $cityNames = $cityQuery->all(\Yii::$app->gmudb);

        $items = [];
        foreach ($cityNames as $city) {
            $items[] = [
                'id' => $city['city'],
                'text' => $city['city'],
            ];
        }

        return $items;
    }

    // public function actionLogError()
    // {
    //     $this->enableCsrfValidation = false;
    //     $request = Yii::$app->request;
    //     if (!$request->isAjax || !$request->isPost) {
    //         return [];
    //     }
    //     $request = $request->post();
    //     $message = $request['message'] ?? '';
    //     $line = $request['line'] ?? '';
    //     $file = $request['file'] ?? '';
    //     $column = $request['column'] ?? '';
    //     $error = $request['error'] ?? '';
    //     $log = ['message' => $message, 'line' => $line, 'file' => $file, 'column' => $column, 'error' => $error];
    //     error_log(json_encode($log) . "\n", 3, Yii::getAlias('@frontend') . '/runtime/logs/js-error.log');
    // }

    public function actionDynamicCta()
    {
        if (!Yii::$app->request->isPost) {
            throw new NotFoundHttpException();
        }

        $request = Yii::$app->request->post();
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!isset($request['slug'])) {
            return [];
        }

        $dynamicCtas = $this->ajaxService->getDynamicCtaTemplate($request['slug']);
        $data = [];

        foreach ($dynamicCtas as $dynamicCta) {
            $data[$dynamicCta->slug] = $this->renderPartial(
                '/partials/dynamic-cta/' . $dynamicCta->template_name,
                [
                    'model' => $dynamicCta,
                    'entityId' => $request['entity_id'] ?? '',
                    'entity' => $request['entity'] ?? '',
                ]
            );
        }

        return [
            'data' => $data,
        ];
    }

    public function actionGetSponsorColleges($colleges = [])
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();
        $isMobile = Yii::$app->devicedetect->isMobile();

        $sponsorColleges = (new SponsorService)->getSponsorColleges($request['stream'] ?? null, $request['course'] ?? null, $request['specialization'] ?? null, $request['state'] ?? null);
        $sponsorCollegesList = $sponsorColleges;

        $firstSponsorList = $this->renderPartial('/college/partials/_college-filter-sponser-list', [
            'models' => $sponsorCollegesList ?? [],
            'isMobile' => $isMobile,
            'request' => $request ?? [],
        ]);

        $secondSponsorList = $this->renderPartial('/college/partials/_sponsor-college-filter-list', [
            'models' => $sponsorCollegesList[1] ?? [],
            'isMobile' => $isMobile,
            'request' => $request ?? [],
        ]);

        $thirdSponsorList = $this->renderPartial('/college/partials/_sponsor-college-filter-list', [
            'models' => $sponsorCollegesList[2] ?? [],
            'isMobile' => $isMobile,
            'request' => $request ?? [],
        ]);

        return [
            'firstSponsorList' => (!empty($firstSponsorList)) ? $firstSponsorList : '',
            'secondSponsorList' => (!empty($secondSponsorList)) ? $secondSponsorList : '',
            'thirdSponsorList' => (!empty($thirdSponsorList)) ? $thirdSponsorList : '',
        ];
    }

    public function actionWriteReviewVerifyOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $request = Yii::$app->request->post();
        $otp = implode('', $request['digit']);

        if (strlen($otp) < 4) {
            return ['success' => false, 'message' => 'Otp Field required'];
        }

        if (isset($request) && !empty($request) && !empty($request['Review']['college_id']) && !empty($request['Review']['course_id'])) {
            $student = Student::find()->where(['phone' => $request['Student']['phone']])->one();
            $review = Review::find()
                ->where(['student_id' => $student->id])
                ->andWhere(['college_id' => $request['Review']['college_id']])
                ->andWhere(['course_id' => $request['Review']['course_id']])
                ->one();


            $model = StudentOtp::find()
                ->where(['student_id' => $student->id])
                ->andWhere(['otp' => (int)$otp])
                ->orderBy(['id' => SORT_DESC])
                ->one();

            if (!$model) {
                return ['success' => false, 'message' => 'Please enter the valid otp'];
            }

            $model->otp_status = StudentOtp::STATUS_OTP_USED;
            $model->save();

            if (!$model) {
                return ['success' => false, 'message' => 'Please enter the valid otp'];
            }

            return ['success' => true, 'model' => $model ?? '', 'review_id' => $review->id ?? '', 'student_id' => $student->id];
        } else {
            return ['success' => false, 'message' => 'Request Empty'];
        }
    }

    public function actionWriteReviewResendOtp()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax) {
            return false;
        }
        $request = Yii::$app->request->post();
        $student = Student::find()->where(['phone' => $request['Student']['phone']])->one();
        $model = StudentOtp::find()->where(['student_id' => $student->id])->orderBy(['id' => SORT_DESC])->one();

        if (!$model) {
            return ['success' => false];
        }
        $model->otp = rand(1000, 9999);

        if ($model->save()) {
            SmsService::sendOtp($student->phone, $model->otp);
            return ['success' => true];
        }

        return ['success' => false];
    }


    public function actionReviewColleges()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $query = new Query();
        $query->select(['id', 'name'])
            ->from('college')
            ->where(['like', 'name', trim($request->get('term'))]);

        $colleges = $query->all();

        $items = [];
        foreach ($colleges as $college) {
            $items[] = [
                'id' => $college['id'],
                'text' => $college['name'],
            ];
        }

        return $items;
    }

    public function actionReviewCourses()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $query = new Query();
        $query->select(['id', 'name'])
            ->from(Course::tableName())
            ->where(['like', 'name', trim($request->get('term'))])
            ->orderBy(['name' => SORT_ASC]);
        $courses = $query->all();

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['name'],
            ];
        }

        return $items;
    }

    public function actionReviewExams()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $query = new Query();
        $query->select(['id', 'name'])
            ->from('exam')
            ->where(['like', 'name', trim($request->get('term'))]);
        $exams = $query->all();

        $items = [];
        foreach ($exams as $exam) {
            $items[] = [
                'id' => $exam['name'],
                'text' => $exam['name'],
            ];
        }

        return $items;
    }

    public function actionLiveApplicationForm()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        $data = null;
        if (isset(Yii::$app->request->queryParams['collegeid'])) {
            $data = $this->collegeService->getLiveApplicationFormData(Yii::$app->request->queryParams['collegeid']);
        }


        if (empty($data)) {
            return '';
        }

        $liveApplication = $this->renderPartial('/college/partials/_live-application', [
            'applications' => $data
        ]);

        return $liveApplication;
    }

    //lead auto fetch course data on course page

    public function actionLeadCourses()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        if ($request->get('entity') != null && $request->get('entity') == 'college' || $request->get('entity') == 'colleges') {
            $query = new Query();
            $query->select(['program.id', 'program.name'])
                ->from(Program::tableName() . ' as program')
                ->leftJoin(ProgramCourseMapping::tableName() . ' as pcm', 'pcm.program_id = program.id')
                ->leftJoin(CollegeProgram::tableName() . ' as cp', 'cp.program_id = program.id')
                ->where(['cp.college_id' => $request->get('entity_id')])
                ->andWhere(['like', 'program.name', trim($request->get('term'))])
                ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                ->groupBy('program.id')
                ->orderBy(['program.id' => SORT_DESC]);

            $courses = $query->all();
        } elseif ($request->get('entity') != null &&  $request->get('entity_id') !== '0' && $request->get('entity') == 'course' || $request->get('entity') == 'courses') {
            $query = new Query();
            $query->select(['id', 'name'])
                ->from(Course::tableName())
                ->where(['id' => $request->get('entity_id')])
                ->orWhere(['parent_id' => $request->get('entity_id')])
                ->andWhere(['like', 'name', trim($request->get('term'))])
                ->andWhere(['status' => Course::STATUS_ACTIVE])
                ->orderBy(['name' => SORT_ASC]);
            $courses = $query->all();
        } elseif ($request->get('entity') != null && $request->get('entity_id') !== '0' && $request->get('entity') == 'exam' || $request->get('entity') == 'exam_detail' || $request->get('entity') == 'exams') {
            $query = new Query();
            $query->select(['course.id as id', 'course.name'])
                ->from(Course::tableName())
                ->leftJoin('exam_stream as exam', 'exam.stream_id = course.stream_id')
                ->where(['exam.exam_id' => $request->get('entity_id')])
                ->andWhere(['like', 'course.name', trim($request->get('term'))])
                ->andWhere(['course.status' => Course::STATUS_ACTIVE])
                ->orderBy(['course.name' => SORT_ASC]);
            $courses = $query->all();
        } elseif ($request->get('entity') != null && $request->get('entity') == 'board' || $request->get('entity') == 'boards') {
            $query = new Query();
            $query->select(['id as id', 'name'])
                ->from(Course::tableName())
                ->where(['in', 'degree', ['bachelors', 'integrated-degree', 'diploma']])
                ->andWhere(['like', 'course.name', trim($request->get('term'))])
                ->andWhere(['course.status' => Course::STATUS_ACTIVE])
                ->orderBy(['course.name' => SORT_ASC]);
            $courses = $query->all();
        } else {
            $query = new Query();
            $query->select(['id', 'name'])
                ->from(Course::tableName())
                ->where(['in', 'course_default_list', [Course::COURSE_DEFAULT_YES]])
                ->andWhere(['like', 'name', trim($request->get('term'))])
                ->andWhere(['status' => Course::STATUS_ACTIVE])
                ->orderBy(['name' => SORT_ASC]);

            $courses = $query->all();
        }

        if (empty($courses) && $request->get('entity') !== 'college') {
            $query = new Query();
            $query->select(['id', 'name'])
                ->from(Course::tableName())
                ->where(['in', 'course_default_list', [Course::COURSE_DEFAULT_YES]])
                ->andWhere(['like', 'name', trim($request->get('term'))])
                ->andWhere(['status' => Course::STATUS_ACTIVE])
                ->orderBy(['name' => SORT_ASC]);

            $courses = $query->all();
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['name'],
            ];
        }

        return $items;
    }

    //lead auto fetch stream data on college course and program pages
    public static function actionLeadCourseCapture()
    {
        $data = [];
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!empty($request->bodyParams) && isset($request->bodyParams['auto_pop_entity_id']) && !empty($request->bodyParams['auto_pop_entity_id'])) {
            $id = $request->bodyParams['auto_pop_entity_id'];
        } else {
            $id = !empty($request->bodyParams) && !empty($request->bodyParams['id']) ? $request->bodyParams['id'] : '';
        }

        if (empty($id)) {
            return ['success' => false];
        }

        $data = Course::find()
            ->select(['stream_id', 'degree', 'specialization_id'])
            ->where(['id' => $id])
            ->one();


        if (empty($data)) {
            return ['success' => false];
        }

        if (!empty($request->bodyParams['program_slug'])) {
            $programSpecialization = Program::find()->where(['slug' => $request->bodyParams['program_slug']])->one();
            $specializationId = !empty($programSpecialization) && !empty($programSpecialization->programCourseMapping) ? $programSpecialization->programCourseMapping->specialization_id : '';
        } else {
            $specializationId = !empty($data->specialization_id) ? $data->specialization_id : '';
        }

        return [
            'stream_id' => $request->bodyParams['entity'] == 'course_stream' ? $id : $data->stream_id ?? '',
            'level' => $request->bodyParams['entity'] == 'course_stream' ? '' : (isset(DataHelper::$levelMappingWithDegreeTableCourse[$data->degree]) ? DataHelper::$levelMappingWithDegreeTableCourse[$data->degree] : ''),
            'specialization_id' => $request->bodyParams['entity'] == 'course_stream' ? '' : $specializationId,
            'success' => true
        ];
    }

    //lead auto fetch exam data on college course and program pages
    public static function actionLeadCourseExamCapture()
    {
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        $course_id = !empty($request->bodyParams) && !empty($request->bodyParams['course_id']) ? $request->bodyParams['course_id'] : [];

        if (!is_numeric($course_id)) {
            $query = new Query();
            $query->select(['cce.exam_id', 'cp.id'])
                ->from(CollegeProgramExam::tableName() . ' as  cce')
                ->leftJoin(CollegeProgram::tableName() . ' as cp', 'cp.id = cce.college_program_id')
                ->leftJoin(Program::tableName() . ' as p', 'p.id = cp.program_id')
                ->where(['p.slug' => $course_id]);

            $examId = $query->one();
        } else {
            $query = new Query();
            $query->select(['ec.exam_id', 'course.id'])
                ->from('exam_course ec')
                ->leftJoin(Course::tableName(), 'course.id = ec.course_id')
                ->where(['course.id' => $course_id]);

            $examId = $query->one();
        }

        return ['success' => true, 'examId' => $examId['exam_id'] ?? ''];
    }


    //lead autofetch board highest qualification based on level

    public static function actionBoardLeadValues()
    {
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        $board_id = !empty($request->bodyParams) && !empty($request->bodyParams['board_id']) ? $request->bodyParams['board_id'] : (!empty($request->bodyParams['auto_pop_entity_id']) ? $request->bodyParams['auto_pop_entity_id'] : '');

        if (empty($board_id)) {
            return ['success' => false];
        }

        $board = Board::find()->select(['level'])->where(['id' => $board_id])->one();
        $boardLevel = !empty($board) ? $board->level : '';

        return ['level' => $boardLevel, 'success' => true];
    }

    //lead autofetch interested location and state  on reviews and news based on collegeId

    public static function actionCollegeCity()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request;

        $college_id = !empty($request->bodyParams) && !empty($request->bodyParams['college_id']) ? $request->bodyParams['college_id'] : (!empty($request->bodyParams['auto_pop_entity_id']) ? $request->bodyParams['auto_pop_entity_id'] : '');

        $query = new Query();
        $query->select(['c.id', 'state.old_id as state_id', 'state.name as stateName', 'c.name as cityName', 'college.logo_image', 'college.slug'])
            ->from('city c')
            ->leftJoin('college as college', 'college.city_id = c.id')
            ->leftJoin('state as state', 'state.id = c.state_id')
            ->where(['college.id' => $college_id]);

        $data = $query->one();

        return [
            'success' => true,
            'collegeCityId' => $data['id'] ?? '',
            'collegeStateId' => $data['state_id'] ?? '',
            'cityName' => $data['cityName'] ?? '',
            'stateName' => $data['stateName'] ?? '',
            'collegeLogo' => $data['logo_image'] ?? '',
            'collegeSlug' => $data['slug'] ?? ''
        ];
    }

    //lead autofetch stream and level on exam pages

    public static function actionExamStreamLevel()
    {
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        $exam_id = !empty($request->bodyParams) && !empty($request->bodyParams['exam_id']) ? $request->bodyParams['exam_id'] : (!empty($request->bodyParams['auto_pop_entity_id']) ? $request->bodyParams['auto_pop_entity_id'] : '');

        $data = Exam::find()
            ->select(['derived_lead_level', 'primary_stream_id', 'primary_course_id'])
            ->where(['exam.id' => $exam_id])
            ->one();

        if ($data == false) {
            return ['success' => false, 'id' => $exam_id ?? ''];
        }

        return [
            'level' => $data->derived_lead_level ?? '',
            'stream_id' => $data->primary_stream_id ?? '',
            'course_id' => $data->primary_course_id ?? '',
            'specialization_id' => !empty($data->course) && !empty($data->course->specialization_id) ? $data->course->specialization_id : '',
            'success' => true
        ];
    }

    //lead autofetch stream and level on exam pages

    public static function actionArticleStreamLevel()
    {
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        $id = !empty($request->bodyParams) && !empty($request->bodyParams['id']) ? $request->bodyParams['id'] : (!empty($request->bodyParams['auto_pop_entity_id']) ? $request->bodyParams['auto_pop_entity_id'] : '');
        $entity = !empty($request->bodyParams) && !empty($request->bodyParams['entity']) ? $request->bodyParams['entity'] : '';

        $query = new Query();
        // $query->select(['category_id', 'stream.id', 'stream.name'])
        //     ->from(Article::tableName())
        //     ->leftJoin(Category::tableName(), 'category.id = article.category_id')
        //     ->leftJoin(Stream::tableName(), 'stream.slug = category.slug')
        //     ->where(['article.id' => $id]);
        $query->select(['stream_id', 'highest_qualification']);
        if ($entity == 'articles') {
            $query->from(Article::tableName());
        } else {
            $query->from(NewsSubdomain::tableName());
        }
        $query->where(['id' => $id]);

        $data = $query->one();

        if ($data == false) {
            return ['success' => false, 'stream_id' => ''];
        }

        return [
            'stream_id' => $data['stream_id'] ?? '',
            'success' => true,
            'level' => !empty($data['highest_qualification']) ? $data['highest_qualification']  : '',
        ];
    }

    public static function actionLeadAutoPopUp()
    {
        $request = Yii::$app->request;

        $entity = !empty($request->bodyParams) && !empty($request->bodyParams['entity']) ? $request->bodyParams['entity'] : [];

        if ($entity == 'course' || $entity == 'college' || $entity == 'course_stream') {
            return self::actionLeadCourseCapture();
        } elseif ($entity == 'exam_detail' || $entity == 'exam') {
            return self::actionExamStreamLevel();
        } elseif ($entity == 'board') {
            return self::actionBoardLeadValues();
        } elseif ($entity == 'articles' || $entity == 'career') {
            return self::actionArticleStreamLevel();
        } elseif ($entity == 'college-listing' || $entity == 'college-admissions') {
            return self::actionAllCollegeLeadValues();
        }
    }

    //lead autofetch all colleges page
    public static function actionAllCollegeLeadValues()
    {
        $data = [];
        $request = Yii::$app->request;

        Yii::$app->response->format = Response::FORMAT_JSON;
        $collegeId = !empty($request->bodyParams) && !empty($request->bodyParams['college_id']) ? $request->bodyParams['college_id'] : null;
        $collegeLevel = '';

        if (!empty($request->bodyParams['stream']) && empty($request->bodyParams['course'])) {
            $stream = Stream::find()
                ->select(['id'])
                ->where(['slug' => $request->bodyParams['stream']])
                ->one();

            if (!empty($stream)) {
                $query = new Query();
                $query->select(['degree.id', 'degree.name', 'stream_id'])
                    ->distinct()
                    ->from([CollegeProgram::tableName() . ' as cp'])
                    ->leftJoin(Course::tableName(), 'course.id = cp.course_id')
                    ->leftJoin('degree', 'degree.slug = course.degree')
                    ->where(['course.stream_id' => $stream->id])
                    ->andWhere(['cp.college_id' => $collegeId])
                    ->andWhere(['degree.status' => Degree::STATUS_ACTIVE])
                    ->orderBy(['degree.id' => SORT_ASC]);

                $collegeLevel = $query->all();
            }

            $data['stream_id'] = !empty($stream) ? $stream->id : '';
            $data['level'] = !empty($request->bodyParams['course_type']) ? $request->bodyParams['course_type'] : (!empty($collegeLevel) && count($collegeLevel) == 1 ? $collegeLevel[0]['id'] : '');
        } else if (!empty($request->bodyParams['examSlug'])) {
            $query = new Query();
            $query->select(['derived_lead_level', 'primary_stream_id', 'primary_course_id', 'id'])
                ->from([Exam::tableName()])
                ->where(['slug' => $request->bodyParams['examSlug']])
                ->one();
            $exam = $query->one();

            $data['level'] = !empty($request->bodyParams['course_type']) ? $request->bodyParams['course_type'] : (!empty($exam) && !empty($exam['derived_lead_level']) ? $exam['derived_lead_level'] : '');
            $data['stream_id'] = !empty($exam) ? $exam['primary_stream_id'] : '';
            $data['course_id'] = !empty($exam) ? $exam['primary_course_id'] : '';
            $data['exam_id'] = !empty($exam) ? $exam['id'] : '';
        } else if (!empty($request->bodyParams['stream']) && !empty($request->bodyParams['course'])) {
            $query = new Query();
            $query->select(['stream_id', 'degree', 'specialization_id'])
                ->from(Course::tableName());

            if (is_numeric($request->bodyParams['course'])) {
                $query->where(['id' => $request->bodyParams['course']]);
            } else {
                $query->where(['slug' => $request->bodyParams['course']]);
            }

            $course = $query->one();

            $data['level'] =  !empty($request->bodyParams['course_type']) ? $request->bodyParams['course_type'] : (!empty($course) ? (isset(DataHelper::$levelMappingWithDegreeTableCourse[$course['degree']]) ? DataHelper::$levelMappingWithDegreeTableCourse[$course['degree']] : '') : '');
            $data['stream_id'] = !empty($course) ? $course['stream_id'] : (!empty($stream) ? $stream->id : '');
            $data['specialization_id'] = !empty($course) ? $course['specialization_id'] : '';
        } else if (!empty($request->bodyParams['course_type'])) {
            $data['stream_id'] = '';
            $data['level'] = !empty($request->bodyParams['course_type']) ? $request->bodyParams['course_type'] : '';
        }

        return [
            'success' => true,
            'data' => $data,
        ];
    }

    //distance education
    public static function actionDistanceEducation()
    {
        $request = Yii::$app->request;
        Yii::$app->response->format = Response::FORMAT_JSON;

        $result = [
            'success' => false,
            'distance_education' => 0
        ];

        $courseId = !empty($request->bodyParams) && isset($request->bodyParams['courseId']) ? $request->bodyParams['courseId'] : [];

        if (empty($courseId)) {
            return $result;
        }

        $course = Course::find()->select(['is_distance_course'])->where(['id' => $courseId])->one();

        if (!empty($course) && isset($course['is_distance_course'])) {
            $result['success'] = true;
            $result['distance_education'] = $course['is_distance_course'];
        }

        return $result;
    }

    public function actionLoadReviews()
    {
        $data = [];
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();
        $reviews = (new ReviewService)->getCollegeReviews($request['college_id'], 10, $request['page'] * 10);

        $data['review'] = $this->renderPartial('/college/partials/_review-sub-card', [
            'reviews' => $reviews
        ]);

        if (($data['review'] == "\n")) {
            $data['review'] = null;
        }
        $data['pageNo'] = $request['page'] + 1;

        return $data;
    }

    public function actionSaveQnaAnswer()
    {

        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!Yii::$app->request->isAjax) {
            return false;
        }

        $model = new QnaAnswer();
        if (Yii::$app->request->post()) {
            $decodedQuestionId = base64_decode(Yii::$app->request->post()['question_id']);
            $questionId = explode('f1nd1ngn320', $decodedQuestionId);
            $model->question_id = $questionId['1'];
            $model->answer = Yii::$app->request->post()['answer'];
            $model->student_id = Yii::$app->user->identity->id;
            $model->status = 0;
            if ($model->validate() && $model->save()) {
                return ['success' => true];
            } else {
                return ['success' => false];
            }
        }
    }

    /**
     * Finds board list on the basis of search parameter.
     * @param string $q
     * @return Board List matched with $q param
     */
    public function actionBoardList()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $boards = Board::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', trim($request->get('term'))])
            ->andWhere(['status' => Board::STATUS_ACTIVE])
            ->all();

        if (empty($boards)) {
            return [];
        }

        $items = [];
        foreach ($boards as $board) {
            $items[] = [
                'id' => $board['id'],
                'text' => $board['display_name'],
            ];
        }

        return $items;
    }

    /**
     * Finds city list on the basis of search parameter.
     * @param string $q
     * @return City List matched with $q param
     */
    public function actionLeadCities()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $citiesData = [];
        $englishPattern = '/^[A-Za-z0-9\s]*$/';
        if (!preg_match($englishPattern, $request->get('term'))) {
            return [];
        }

        $cities = City::find()
            ->select(['id', 'name', 'state_id'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->andWhere(['status' => City::STATUS_ACTIVE])
            ->with('state')
            ->all();

        if (empty($cities)) {
            return [];
        }

        foreach ($cities as $city) {
            $citiesData[] = [
                'id' => $city->id,
                'text' => $city->name,
                'state_name' => $city->state->name,
            ];
        }

        return ['states' => $this->groupCitiesByState($citiesData)];
    }

    // Helper function to group cities by state
    private function groupCitiesByState($citiesData)
    {
        $groupedCities = [];

        foreach ($citiesData as $city) {
            $stateName = $city['state_name'];
            unset($city['state_name']);

            $groupedCities[$stateName][] = $city;
        }

        return array_map(function ($state, $cities) {
            return ['state_name' => $state, 'cities' => $cities];
        }, array_keys($groupedCities), $groupedCities);
    }

    /**
     * Finds Course list on the basis of search parameter.
     * @param string $q
     * @return Course List matched with $q param
     */
    public function actionCourseList()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $courses = Course::find()
            ->select(['id', 'short_name'])
            ->where(['like', 'short_name', trim($request->get('term'))])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->all();

        if (empty($courses)) {
            return [];
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['short_name'],
            ];
        }

        return $items;
    }

    /**
     * Finds College list on the basis of search parameter.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionCollegeList()
    {
        $items = [];
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;


        $colleges = CollegeSQL::find()
            ->select(['id', 'display_name'])
            ->where(['like', 'display_name', trim($request->get('term'))])
            ->andWhere(['status' => CollegeSQL::STATUS_ACTIVE])
            ->all();

        if (empty($colleges)) {
            return [];
        }

        foreach ($colleges as $college) {
            $items[] = [
                'id' => $college['id'],
                'text' => $college['display_name'],
            ];
        }
        return $items;
    }

    public function actionLoadCta()
    {
        if (!Yii::$app->request->isPost) {
            throw new NotFoundHttpException();
        }

        $request = Yii::$app->request->post();

        if (!empty($request['lead_cta']) && !empty($request['pageName']) && $request['pageName'] === 'courses-fees') {
            $array = implode(',', array_unique(explode(',', $request['lead_cta'])));
            $lead_cta = explode(',', $array);
        } else {
            $lead_cta = !empty($request['lead_cta']) ? array_unique($request['lead_cta']) : '';
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        if (empty($lead_cta)) {
            return [];
        }

        foreach ($lead_cta as $key => $value) {
            $data[$value] = $this->renderPartial('/' . $request['data_entity'] . '/partials/_lead-cta', [
                'entity' => $request['entity'] ?? '',
                'entity_id' => $request['entity_id'] ?? '',
                'slug' => $request['slug'] ?? '',
                'name' => $request['displayName'] ?? '',
                'dynamicCta' => $request['dynamicCta'] ?? [],
                'lead_cta' => $value ?? '',
                'pageName' => $request['pageName'] ?? '',
                'data_entity' => $request['data_entity'] ?? '',
                'sponsorClientUrl' => $request['sponsorClientUrl'] ?? '',
                'image' => $request['image'] ?? '',
                'durl' => $request['durl'] ?? '',
                'city' => $request['city'] ?? null,
                'state' => $request['state'] ?? null,
                'courseId' => $request['courseId'] ?? null,
                'programSlug' => $request['programSlug'] ?? null,
                'specialization' => $request['specialization'] ?? null
            ]);
        }
        return [
            'data' => $data ?? [],
            'entity' => $request['data_entity'] ?? '',
        ];
    }

    public function actionLoadFilterCta()
    {
        if (!Yii::$app->request->isPost) {
            throw new NotFoundHttpException();
        }

        $request = Yii::$app->request->post();

        if ($request['pageName'] === 'courses-fees') {
            $array = implode(',', array_unique(explode(',', $request['lead_cta'])));
            $lead_cta = explode(',', $array);
        } else {
            $lead_cta = !empty($request['lead_cta']) ? array_unique($request['lead_cta']) : [];
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        if (empty($lead_cta)) {
            return [];
        }

        foreach ($lead_cta as $key => $value) {
            $data[$value] = $this->renderPartial('/' . $request['data_entity'] . '/partials/_lead-cta', [
                'entity' => $request['entity'] ?? '',
                'entity_id' => $request['entity_id'] ?? '',
                'slug' => $request['slug'] ?? '',
                'name' => $request['displayName'] ?? '',
                'dynamicCta' => $request['dynamicCta'] ?? [],
                'lead_cta' => $value ?? '',
                'pageName' => $request['pageName'] ?? '',
                'data_entity' => $request['data_entity'] ?? '',
                'sponsorClientUrl' => $request['sponsorClientUrl'] ?? '',
                'image' => $request['image'] ?? '',
                'durl' => $request['durl'] ?? '',
                'city' => $request['city'] ?? null,
                'state' => $request['state'] ?? null,
                'course' => $request['course'] ?? null,
                'programSlug' => $request['programSlug'] ?? null,
                'specialization' => $request['specialization'] ?? null,
                'stream' => $request['stream'] ?? null
            ]);
        }

        return [
            'data' => $data ?? [],
            'entity' => $request['data_entity'] ?? '',
        ];
    }

    /** Get the exam Based Cut off data */
    public function actionGetExamBasedCutOff()
    {
        $data = [];
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();

        if (empty($request['college_id'])) {
            return false;
        }

        $query = new Query();
        $query->select(['gender', 'category', 'opening_rank', 'closing_rank', 'percentile', 'closing_score'])
            ->from('cutoff_detail')
            ->where(['college_id' => $request['college_id']])
            ->andWhere(['round' => $request['round']])
            ->andWhere(['not', ['category' => null]])
            ->andWhere(['exam_id' => $request['exam']])
            ->andWhere(['year' => $request['year']]);

        if (!empty($request['spec'])) {
            $query->andWhere(['specialization_id' => $request['spec']]);
        } else {
            $query->andWhere(['course_id' => $request['course']]);
        }
        $data = $query->all();

        $finalData = [];
        if (empty($data)) {
            return false;
        }
        $items = [];
        foreach ($data as $d) {
            if (!empty($d['closing_score'])) {
                $items['scoreArr'][] = [
                    'category' => $d['category'],
                    'gender' => $d['gender'],
                    'closing_score' => $d['closing_score'],
                ];
            } else if (!empty($d['percentile'])) {
                $items['percentileArr'][] = [
                    'category' => $d['category'],
                    'gender' => $d['gender'],
                    'percentile' => $d['percentile'],
                ];
            } else {
                $items['opclArr'][] = [
                    'category' => $d['category'],
                    'gender' => $d['gender'],
                    'open_rank' => $d['opening_rank'],
                    'close_rank' => $d['closing_rank'],
                ];
            }
        }

        if (!empty($items['opclArr'])) {
            return $this->renderPartial('/college/partials/_cut-off-open-closing-table', ['data' => $items['opclArr']]);
        } else if (!empty($items['scoreArr'])) {
            return $this->renderPartial('/college/partials/_cut-off-closing-score-table', ['data' => $items['scoreArr']]);
        } else if (!empty($items['percentileArr'])) {
            return $this->renderPartial('/college/partials/_cut-off-percent-table', ['data' => $items['percentileArr']]);
        } else {
            return false;
        }
    }

    public function actionGetCollegeInfo()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        if (!empty($request['college_ids'])) {
            foreach ($request['college_ids'] as $id) {
                $college[] = CollegeSQL::find()->select(['slug'])->byId($id)->one();
            }

            return [
                'college_one' => !empty($college) && !empty($college[0]) ? $college[0]->slug : '',
                'college_two' => !empty($college) && !empty($college[1]) ? $college[1]->slug : '',
            ];
        } else {
            foreach ($request['college_slugs'] as $slug) {
                $college[] = CollegeSQL::find()->select(['id', 'display_name'])->where(['slug' => $slug])->one();
            }

            foreach ($request['program_ids'] as $id) {
                $program[] = Program::find()->select(['name'])->where(['id' => $id])->one();
            }

            return [
                'college_one' => [
                    'id' => $college[0]->id ?? '',
                    'name' => $college[0]->display_name ?? ''
                ],
                'college_two' => [
                    'id' => $college[1]->id ?? '',
                    'name' => $college[1]->display_name ?? ''
                ],
                'program_one' => [
                    'name' => $program[0]->name ?? ''
                ],
                'program_two' => [
                    'name' => $program[1]->name ?? ''
                ],
            ];
        }

        return [];
    }

    public function actionSaveCollegeShortlistData()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $studentCollegeShortlist = new StudentCollegeShortlist();
        $studentCollegeShortlist->student_id = $request['student_id'];
        $studentCollegeShortlist->activity_id = $request['activity_id'];
        $studentCollegeShortlist->college_id = $request['college_id'];
        $studentCollegeShortlist->sponsored = 0;

        if ($studentCollegeShortlist->save()) {
            return true;
        } else {
            return false;
        }
    }

    public function actionLeadStream()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;
        $entityArray = ['college-compare', 'review', 'board', 'boards', 'articles', 'ncert', 'career', 'login', 'news', 'course', 'course_stream', 'scholarships', 'home', 'exam_landing', 'exam', 'exam_detail', 'exams', 'qna', 'olympiad'];
        $items = [];

        if ($request->get('entity') != null && $request->get('entity') == 'college' || $request->get('entity') == 'colleges' || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') !== '0')) {
            $query = new Query();
            $query->select(['stream.name', 'stream.id'])
                ->distinct()
                ->from([CollegeProgram::tableName() . ' as cp'])
                ->leftJoin(Course::tableName(), 'course.id = cp.course_id')
                ->leftJoin(Stream::tableName(), 'stream.id = course.stream_id')
                ->where(['cp.college_id' => $request->get('entity_id')])
                ->andWhere(['not', ['stream.slug' => 'other']])
                ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                ->andWhere(['like', 'stream.name', trim($request->get('term'))])
                ->andWhere(['not', ['stream.id' => null]]);

            $collegeStreams = $query->all();

            if (empty($collegeStreams) && ($request->get('entity_id') == '' || $request->get('entity_id') == 0)) {
                $collegeStreams = self::getStreamLeadDropDown($request);
            }

            foreach ($collegeStreams as $stream) {
                $items[] = [
                    'id' => $stream['id'],
                    'text' => $stream['name'],
                ];
            }

            return $items;
        } elseif ($request->get('entity') != null && in_array($request->get('entity'), $entityArray) || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') == '0')) {
            $defaultStreamList = self::getStreamLeadDropDown($request);

            $items = [];
            foreach ($defaultStreamList as $stream) {
                $items[] = [
                    'id' => $stream['id'],
                    'text' => $stream['name'],
                ];
            }
            return $items;
        }

        return $items;
    }

    public function actionLeadLevel()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;
        $entityArray = ['college-compare', 'review', 'board', 'boards', 'articles', 'ncert', 'career', 'login', 'news', 'exam_landing', 'course', 'course_stream', 'scholarships', 'home', 'qna', 'olympiad'];
        $items = '';

        if ($request->get('entity') != null && $request->get('entity') == 'college' || $request->get('entity') == 'colleges' || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') !== '0')) {
            $query = new Query();
            $query->select(['degree.id', 'degree.name', 'stream_id'])
                ->distinct()
                ->from([CollegeProgram::tableName() . ' as cp'])
                ->leftJoin(Course::tableName(), 'course.id = cp.course_id')
                ->leftJoin('degree', 'degree.slug = course.degree')
                ->where(['course.stream_id' => $request->get('stream_id')])
                ->andWhere(['cp.college_id' => $request->get('entity_id')])
                ->andWhere(['like', 'degree.name', trim($request->get('term'))])
                ->andWhere(['not', ['degree.slug' => 'other']])
                ->andWhere(['degree.status' => Degree::STATUS_ACTIVE])
                ->orderBy(['degree.id' => SORT_ASC]);

            $collegeLevel = $query->all();

            if (empty($collegeLevel) && ($request->get('college_id') == 'undefined' || $request->get('entity_id') == '' || $request->get('college_id') == 0 || $request->get('college_id') == '')) {
                $collegeLevel = self::getLevelLeadDropDown($request);
            }

            $items = [];

            foreach ($collegeLevel as $level) {
                $items[] = [
                    'id' => $level['id'],
                    'text' => $level['name'],
                ];
            }
            return $items;
        } elseif ($request->get('entity') != null && $request->get('entity_id') !== '0' && $request->get('entity') == 'exam' || $request->get('entity') == 'exam_detail' || $request->get('entity') == 'exams') {
            $query = new Query();
            $query->select(['degree.id', 'degree.name'])
                ->distinct()
                ->from([Exam::tableName()])
                ->leftJoin('degree', 'degree.id = exam.derived_lead_level')
                ->where(['exam.primary_stream_id' => $request->get('stream_id')])
                ->andWhere(['like', 'degree.name', trim($request->get('term'))])
                ->andWhere(['not', ['degree.id' => null]])
                ->andWhere(['not', ['degree.slug' => 'other']])
                ->orderBy(['degree.id' => SORT_ASC]);

            $examLevels = $query->all();

            if (empty($examLevels) || (!empty($request->get('page_name') && $request->get('page_name') == 'exam-category'))) {
                $examLevels = self::getLevelLeadDropDown($request);
            }
            $items = [];
            foreach ($examLevels as $level) {
                $items[] = [
                    'id' => $level['id'],
                    'text' => $level['name'],
                ];
            }
            return $items;
        } elseif ($request->get('entity') != null && in_array($request->get('entity'), $entityArray) || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') == '0')) {
            $levels = self::getLevelLeadDropDown($request);
            $items = [];

            foreach ($levels as $level) {
                $items[] = [
                    'id' => $level['id'],
                    'text' => $level['name'],
                ];
            }
            return $items;
        }

        return $items;
    }

    public function actionLeadSpecialization()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $entityArray = ['college-compare', 'review', 'board', 'boards', 'articles', 'ncert', 'career', 'login', 'news', 'course', 'course_stream', 'scholarships', 'home', 'exam_landing', 'exam', 'exam_detail', 'exams', 'olympiad'];
        $items = [];

        if ($request->get('entity') != null && $request->get('entity') == 'college' || $request->get('entity') == 'colleges' || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') !== '0')) {
            $levelFlip = array_flip(DataHelper::$levelMappingWithDegreeTableCourse);
            if ($request->get('level')) {
                $level = $levelFlip[$request->get('level')];
            }

            $query = new Query();
            $query->select(['sp.id', 'sp.name'])
                ->from([CollegeProgram::tableName()])
                ->leftJoin(ProgramCourseMapping::tableName() . ' as pcm', 'pcm.program_id=college_program.program_id')
                ->leftJoin(SpecializationNew::tableName() . ' as sp', 'sp.id=pcm.specialization_id')
                ->leftJoin(Course::tableName() . ' as c', 'c.id=pcm.course_id')
                ->where(['college_program.college_id' => $request->get('entity_id')])
                ->andWhere(['like', 'sp.name', trim($request->get('term'))])
                ->andWhere(['not', ['pcm.specialization_id' => null]])
                ->andWhere(['c.stream_id' => $request->get('stream_id')])
                ->andWhere(['college_program.status' => CollegeProgram::STATUS_ACTIVE]);
            if ($level) {
                $query->andWhere(['c.degree' => $level]);
            }
            $query->groupBy(['sp.id']);

            $collegeSpecializations = $query->all();

            if (empty($collegeSpecializations) && !empty($request->get('entity_id')) && ($request->get('entity') == 'college' || $request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions')) {
                $queryOne = new Query();
                $queryOne->select(['sp.id', 'sp.name'])
                    ->from([CollegeProgram::tableName()])
                    ->leftJoin(ProgramCourseMapping::tableName() . ' as pcm', 'pcm.program_id=college_program.program_id')
                    ->leftJoin(SpecializationNew::tableName() . ' as sp', 'sp.id=pcm.specialization_id')
                    ->where(['college_program.college_id' => $request->get('entity_id')])
                    ->andWhere(['like', 'sp.name', trim($request->get('term'))])
                    ->andWhere(['not', ['pcm.specialization_id' => null]])
                    ->andWhere(['college_program.status' => CollegeProgram::STATUS_ACTIVE])
                    ->groupBy(['sp.id']);

                $collegeSpecializations = $queryOne->all();
            } elseif (empty($collegeSpecializations) && ($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('college_id') !== null && ($request->get('college_id') == 'undefined' || $request->get('college_id') == '') || $request->get('entity_id') == '') { //mobile sticky CTA
                $collegeSpecializations = self::getSpecializationLeadDropDown($request);
            }
            $items = [];

            foreach ($collegeSpecializations as $specialization) {
                $items[] = [
                    'id' => $specialization['id'],
                    'text' => $specialization['name'],
                ];
            }

            return $items;
        } elseif ($request->get('entity') != null && in_array($request->get('entity'), $entityArray) || (($request->get('entity') == 'college-listing' || $request->get('entity') == 'college-admissions') && $request->get('entity_id') == '0')) {
            $defaultSpecializationList = self::getSpecializationLeadDropDown($request);

            $items = [];
            foreach ($defaultSpecializationList as $specialization) {
                $items[] = [
                    'id' => $specialization['id'],
                    'text' => $specialization['name'],
                ];
            }
            return $items;
        }

        return $items;
    }

    public function getStreamLeadDropDown($request)
    {
        $otherStreamSlug = 'other';

        $defaultStreamList = Stream::find()
            ->select(['id', 'name'])
            ->where(['like', 'stream.name', trim($request->get('term'))])
            ->byExcludeSlug($otherStreamSlug)
            ->andWhere(['stream.status' => Stream::STATUS_ACTIVE])
            ->orderBy(['stream.name' => SORT_ASC])->all();

        return $defaultStreamList ?? [];
    }

    public function getSpecializationLeadDropDown($request)
    {
        $defaultSpecializationList = SpecializationNew::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->andWhere(['status' => SpecializationNew::STATUS_ACTIVE])
            ->orderBy(['name' => SORT_ASC])
            ->all();

        return $defaultSpecializationList ?? [];
    }

    public function getLevelLeadDropDown($request)
    {
        $otherLevelSlug = 'other';

        $defualtLevelList = Degree::find()
            ->select(['id', 'name'])
            ->where(['like', 'degree.name', trim($request->get('term'))])
            ->byExcludeSlug($otherLevelSlug)
            ->andWhere(['degree.status' => Degree::STATUS_ACTIVE])
            ->orderBy(['degree.id' => SORT_ASC])
            ->all();

        return $defualtLevelList ?? [];
    }

    public function actionCommonApplicationFormDegree()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        $request = \Yii::$app->request;

        $excludeSlug = ['other', 'tenth', 'eleventh', 'twelfth'];

        $degrees = Degree::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->andWhere(['status' => Degree::STATUS_ACTIVE])
            ->andWhere(['NOT IN', 'slug', $excludeSlug])
            ->orderBy(['name' => SORT_ASC])
            ->all();

        if (empty($degrees)) {
            return [];
        }

        $items = [];
        foreach ($degrees as $degree) {
            $items[] = [
                'id' => $degree['id'],
                'text' => $degree['name'],
            ];
        }
        return $items;
    }

    public function actionCommonApplicationFormCourses()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        $request = \Yii::$app->request;

        $courses = Course::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->andWhere(['status' => Course::STATUS_ACTIVE])
            ->orderBy(['name' => SORT_ASC])
            ->limit(20)
            ->asArray()
            ->all();

        if (empty($courses)) {
            return [];
        }

        $items = [];
        foreach ($courses as $course) {
            $items[] = [
                'id' => $course['id'],
                'text' => $course['name'],
            ];
        }
        return $items;
    }

    public function actionShowCollegeCompareSelectPanel()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $isMobile = Yii::$app->devicedetect->isMobile();

        $headerSelectPanel = $this->renderPartial('/college/partials/_college_compare_header_select_panel', [
            'isMobile' => $isMobile,
        ]);

        return [
            'headerSelectPanel' => (!empty($headerSelectPanel)) ? $headerSelectPanel : '',
        ];
    }

    public function actionSearchAdmissionCollege($type, $q = null)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;
        $response = [];
        $response = $this->ajaxService->getAdmissionCollegesSearch($request->bodyParams['q']);
        return Json::encode($response);
    }

    public function actionGetMediaDriveFile()
    {
        $dataArray = ['news', 'articles'];
        $mediaFiles = [];
        $params = Yii::$app->request->post();
        $entityId = isset(DataHelper::$ctEntityIdMapping[$params['entity']]) ? DataHelper::$ctEntityIdMapping[$params['entity']] : '';
        $replacements = [
            '<span class="spriteIcon applyRedIcon"></span>' => '{list}',
            '<span class="spriteIcon whiteDownloadIcon redDownloadIcon"></span>' => '{download}',
            '<span class="spriteIcon applyWhiteIconCta"></span>' => '{white_list}'
        ];
        $cta_text = str_replace(array_keys($replacements), array_values($replacements), $params['cta_text']);
        $ctaTextFinal = empty($entityId) || !isset(DataHelper::$ctaOtherCategoryarray[$entityId]) ? [] : UserService::cleanText($cta_text, DataHelper::$ctaOtherCategoryarray[$entityId]);

        $ctaId = LeadBucketCtaDetail::find()
            ->select(['id'])
            ->where(['cta_title' => $params['cta_title']])
            ->scalar();

        if ($ctaId && in_array($params['primary_entity'], $dataArray)) {
            $uploadTypeId = MediaDriveCtaTypeMapping::find()
                ->select(['upload_type_id'])
                ->where(['cta_id' => $ctaId, 'entity' => $params['entity'], 'status' => MediaDrive::STATUS_ACTIVE])
                ->scalar();
        } else {
            $uploadTypeId = MediaDriveUploadType::find()->select(['id'])
                ->where([
                    'entity' => $params['entity'],
                    'status' => MediaDrive::STATUS_ACTIVE
                ])
                ->andWhere(new \yii\db\Expression("upload_type REGEXP '" . preg_quote($ctaTextFinal['ctaTextFinal'], '/') . "?'"))
                ->scalar();
        }
        if ($uploadTypeId) {
            $mediaFiles = MediaDrive::find()
                ->select(['file_name'])
                ->where([
                    'entity' => $params['entity'],
                    'entity_id' => $params['entity_id'],
                    'sub_page' => $uploadTypeId,
                    'status' => MediaDrive::STATUS_ACTIVE
                ])
                ->orderBy(['id' => SORT_DESC])
                ->limit(10)
                ->column();

            if ($mediaFiles) {
                return $this->handleMediaFiles($mediaFiles, $params['entity']);
            }
        }

        if ($params['media'] === 'page_download') {
            $pageDownloadResult = $this->handlePageDownload($params);
            $pageDownloadResult = json_decode($pageDownloadResult, true);

            if ($pageDownloadResult['status'] !== 'Failed') {
                return Json::encode($pageDownloadResult);
            }
        }

        if (in_array($params['primary_entity'], ['articles', 'news'])) {
            return $this->handlePrimaryEntityDownload($params);
        } else {
            return $this->handleEntityPageDownload($params);
        }

        return Json::encode(['status' => 'Failed', 'reason' => 'Path not found']);
    }

    /**
     * Handles the media file response.
     */
    private function handleMediaFiles($mediaFiles, $entity)
    {
        if (count($mediaFiles) === 1) {
            $file = DataHelper::s3Path($mediaFiles[0], 'downloadables', true);
            return Json::encode(['status' => 'Success', 'filePath' => $file, 'isDelete' => 'No']);
        }

        $zipFileName = $entity . '-' . md5(uniqid(rand(), true)) . '.zip';
        $zipFilePath = Yii::getAlias('@pdfAssets') . '/' . $zipFileName;

        if ($this->createZip($mediaFiles, $zipFilePath)) {
            return Json::encode(['status' => 'Success', 'filePath' => Url::base(true) . '/yas/pdf/' . $zipFileName, 'isDelete' => 'Yes']);
        }

        return Json::encode(['status' => 'Failed', 'message' => 'Could not create ZIP file']);
    }

    /**
     * Creates a ZIP archive.
     */
    private function createZip($mediaFiles, $zipFilePath)
    {
        $zip = new ZipArchive();
        $result = $zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
        if ($result === true) {
            foreach ($mediaFiles as $filePath) {
                $fileUrl = DataHelper::s3Path($filePath, 'downloadables', true);
                $content = file_get_contents($fileUrl);

                if ($content !== false) {
                    $zip->addFromString($filePath, $content);
                } else {
                    Yii::error("Failed to fetch file: $filePath");
                }
            }
            $zip->close();
            return true;
        }
    }

    /**
     * Handles page download logic.
     */
    private function handlePageDownload($params)
    {
        $entity = ($params['entity'] === 'exams') ? 'exam' : $params['entity'];
        $column = DataHelper::$ctaEntityFieldsArr[$entity] ?? null;

        if (!$column) {
            return Json::encode(['status' => 'Failed', 'reason' => 'Invalid entity']);
        }

        $cleanedCtaText = trim(preg_replace('/<span\b[^>]*>.*?<\/span>/i', '', $params['cta_text']));
        $modelClass = '\common\models\\' . ucfirst($column['model']);

        $result = $modelClass::find()
            ->select(['id', 'content'])
            ->where([$column['where'] => $params['entity_id']])
            ->andWhere([$column['column_name'] => $cleanedCtaText])
            ->andWhere(['status' => 1])
            ->one();

        if (!$result) {
            return Json::encode(['status' => 'Failed', 'reason' => 'Path not found']);
        }

        $content = ContentHelper::htmlDecode($result->content);
        $content = str_replace('#year', '2025', $content);

        return $this->generatePdf($entity, ContentHelper::htmlDecode($content));
    }

    /**
     * Handles entity download logic.
     */
    private function handleEntityPageDownload($params)
    {

        $idField = empty($params['entity_subpage']) ? '' : (is_numeric($params['entity_subpage']) ? 'id' : 'page_slug');

        $entityMap = [
            'exam' => ['model' => ExamContent::class, 'idField' => 'exam_id', 'pageField' => 'slug', 'hasH1' => false],
            'course' => ['model' => CourseContent::class, 'idField' => 'course_id', 'pageField' => 'page', 'hasH1' => true],
            'board' => ['model' => BoardContent::class, 'idField' => 'board_id', 'pageField' => $idField ?? 'page_slug', 'hasH1' => true],
        ];

        if (!isset($entityMap[$params['entity']])) {
            return Json::encode(['status' => 'Failed', 'reason' => 'Invalid entity type']);
        }

        $entityData = $entityMap[$params['entity']];
        $fields = ['content', $entityData['pageField']];

        if ($entityData['hasH1']) {
            $fields[] = 'h1';
        }

        $content = $entityData['model']::find()
            ->select($fields)
            ->where([$entityData['idField'] => $params['entity_id']])
            ->andWhere([$entityData['pageField'] => $params['entity_subpage']])
            ->one();

        if (!$content || empty($content->content)) {
            return Json::encode(['status' => 'Failed', 'reason' => 'Content not found or empty']);
        }

        $slug = $content->{$entityData['pageField']};
        $htmlContent = '<html><body>';

        if ($entityData['hasH1'] && !empty($content->h1)) {
            $htmlContent .= '<h1>' . ContentHelper::htmlDecode(stripslashes($content->h1), false) . '</h1>';
        }

        $content->content = str_replace(['{year}', '#year'], '2025', $content->content);

        $htmlContent .= $content->content . '</body></html>';

        return $this->generatePdf($slug, $htmlContent);
    }

    /**
     * Handles primary entity download logic.
     */
    private function handlePrimaryEntityDownload($params)
    {
        $primaryEntity = $params['primary_entity'];
        $primaryEntityId = $params['primary_entity_id'];

        if ($primaryEntity === 'articles') {
            $content = Article::find()
                ->select(['slug', 'description', 'h1'])
                ->where(['id' => $primaryEntityId])
                ->one();

            if (!$content || empty($content->description)) {
                return Json::encode(['status' => 'Failed', 'reason' => 'Content not found or empty']);
            }

            $slug = $content->slug;
            $htmlContent = '<html><body><h1>' . ContentHelper::htmlDecode(stripslashes($content->h1), false) . '</h1>' . $content->description . '</body></html>';
        } else {
            $content = NewsContentSubdomain::find()
                ->select(['content', 'h1'])
                ->where(['news_id' => $primaryEntityId])
                ->one();

            if (!$content || empty($content->content)) {
                return Json::encode(['status' => 'Failed', 'reason' => 'Content not found or empty']);
            }

            $slug = 'news';
            $htmlContent = '<html><body><h1>' . ContentHelper::htmlDecode(stripslashes($content->h1), false) . '</h1>' . $content->content . '</body></html>';
        }

        return $this->generatePdf($slug, $htmlContent);
    }

    /**
     * Generates a PDF file.
     */
    private function generatePdf($slug, $content)
    {
        $pdfFileName = $slug . '-' . md5(uniqid(rand(), true)) . '.pdf';
        $pdfFilePath = Yii::getAlias('@pdfAssets') . '/' . $pdfFileName;

        $mpdf = new Mpdf(['tempDir' => Yii::getAlias('@pdfAssets') . '/custom_tmp']);
        $mpdf->WriteHTML($content);
        $mpdf->Output($pdfFilePath, \Mpdf\Output\Destination::FILE);

        return Json::encode([
            'status' => 'Success',
            'filePath' => Url::base(true) . '/yas/pdf/' . $pdfFileName,
            'isDelete' => 'Yes'
        ]);
    }

    public function actionDeleteTempFile()
    {
        $params = Yii::$app->request->post();
        $filePath = Yii::getAlias('@pdfAssets') . '/' . $params['file'];
        if (file_exists($filePath)) {
            unlink($filePath);
            return Json::encode(['status' => 'Success']);
        } else {
            return Json::encode(['status' => 'Error', 'message' => 'File not found']);
        }
    }

    public function actionDeleteBulkTempFile()
    {
        $params = Yii::$app->request->post();
        $results = [];

        if (!empty($params['files']) && is_array($params['files'])) {
            foreach ($params['files'] as $file) {
                $filePath = Yii::getAlias('@pdfAssets') . '/' . $file;
                if (file_exists($filePath)) {
                    unlink($filePath);
                    $results[$file] = 'Deleted';
                } else {
                    $results[$file] = 'Not found';
                }
            }
            return Json::encode(['status' => 'Success', 'results' => $results]);
        }

        return Json::encode(['status' => 'Error', 'message' => 'Invalid input']);
    }

    public function actionQnsPdf()
    {
        $params = Yii::$app->request->post();
        $subTopic = ArticleSubpageSubsectionSubtopic::find()->select(['name', 'id', 'article_subpage_section_id'])->where(['id' => $params['subTopicID']])->one();
        $section_id = $subTopic->article_subpage_section_id;
        $subSection =  ArticleSubpageSection::find()->select(['name', 'article_subpage_id'])->where(['id' => $section_id])->one();
        $subArrticlePage =  ArticleSubpage::find()->select(['name'])->where(['id' => $subSection->article_subpage_id])->one();
        $qnsAns = ArticleSubpageSubsectionQuesAns::find()->select(['question', 'answer'])->where(['article_subpage_section_subtopic_id' => $subTopic->id])->offset(($params['pageIndex']) * 10)->limit(10)->all();
        $htmlContent =  $this->renderPartial('/article/partials/_pdf-qus-ans', [
            'qnsAns' => $qnsAns ?? [],
            'subSection' => $subSection ?? [],
            'subArrticlePage' => $subArrticlePage ?? [],
            'subTopic' => $subTopic ?? [],
        ]);
        $mpdf = new Mpdf(['tempDir' => Yii::getAlias('@pdfAssets') . '/custom_tmp',]);
        $mpdf->WriteHTML($htmlContent);
        $newFileName = $subArrticlePage->name . '_' . $subSection->name . '_' . $subTopic->name . '.pdf';
        $newFileName = preg_replace('/\s+/', '_', $newFileName);
        $pdfFilePath = Yii::getAlias('@pdfAssets') . '/' . $newFileName;
        $mpdf->Output($pdfFilePath, \Mpdf\Output\Destination::FILE);
        return Json::encode(['status' => 'Success', 'filePath' => Url::toDomain() . 'yas/pdf/' . $newFileName, 'isDelete' => 'Yes', 'fileName' => $newFileName]);
    }
    public function actionSaveQuesAns()
    {
        $request = Yii::$app->request;
        if (!$request->isAjax) {
            return false;
        }
        $params = Yii::$app->request->post();
        $studentID = Yii::$app->user->id ?? $params['student_id'];
        $exitsCorrectAttempts = UserAttempts::find()
            ->where(['question_id' => $params['questionID']])
            ->andWhere(['student_id' => $studentID])
            // ->andWhere(['is_correct'=>UserAttempts::CORRECT_ANSWER])
            ->count();

        if ($exitsCorrectAttempts < 1) {
            $attemptModel =  new UserAttempts();
            $attemptModel->question_id = $params['questionID'];
            $attemptModel->student_id =  $studentID;
            $attemptModel->user_input = $params['dataValue'];
            if ($params['dataValue'] == $params['answer']) {
                $attemptModel->is_correct = UserAttempts::CORRECT_ANSWER;
            } else {
                $attemptModel->is_correct = UserAttempts::INCORRECT_ANSWER;
            }
            if ($attemptModel->save()) {
                $exitsIncorrectAttemptsTotal = UserAttempts::find()
                    ->where(['question_id' => $params['questionID']])
                    ->andWhere(['is_correct' => UserAttempts::INCORRECT_ANSWER])
                    ->count();
                $exitsCorrectAttemptsTotal = UserAttempts::find()
                    ->where(['question_id' => $params['questionID']])
                    ->andWhere(['is_correct' => UserAttempts::CORRECT_ANSWER])
                    ->count();
                $isQuestionExits = QuestionMetrics::find()
                    ->where(['question_id' => $params['questionID']])
                    ->one();
                if (!empty($isQuestionExits)) {
                    $isQuestionExits->incorrect_attempts = $exitsIncorrectAttemptsTotal;
                    $isQuestionExits->correct_attempts = $exitsCorrectAttemptsTotal;
                    $isQuestionExits->attempts = $exitsIncorrectAttemptsTotal + $exitsCorrectAttemptsTotal;
                    $isQuestionExits->save();
                } else {
                    $questionMetricsModel =  new QuestionMetrics();
                    $questionMetricsModel->question_id = $params['questionID'];
                    $questionMetricsModel->incorrect_attempts = $exitsIncorrectAttemptsTotal;
                    $questionMetricsModel->correct_attempts = $exitsCorrectAttemptsTotal;
                    $questionMetricsModel->attempts = $exitsIncorrectAttemptsTotal + $exitsCorrectAttemptsTotal;
                    $questionMetricsModel->impression = null;
                    $questionMetricsModel->save();
                }
            }
        }
    }
    //sa colleges
    public function actionShowAbroadColleges()
    {
        $data = [];
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();
        $colleges = (new StudyAbroadService)->getColleges($request['country_id'], 8, $request['page'] * 10);
        $country = (new StudyAbroadService)->getSaCountry('', $request['country_id']);

        $data['college'] = $this->renderPartial('/study-abroad/partials/_more_college', [
            'colleges' => $colleges,
            'country' => $country
        ]);

        if (($data['college'] == "\n")) {
            $data['college'] = null;
        }
        $data['pageNo'] = $request['page'] + 1;

        return $data;
    }

    //sa-college-search

    public function actionSaCollegeSearch($q, $country)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $countryName = SaCountry::find()->where(['id' => $country])->one();

        $models = SaCollege::find()
            ->select(['slug', 'name'])
            ->where(['like', 'name', $q])
            ->andWhere(['sa_country_id' => $country])
            ->all();

        if (empty($models)) {
            return [];
        }

        $data = [];
        foreach ($models as $model) {
            $data[] = [
                'name' => $model->name,
                'url' => \YII::$app->params['siteUrl'] . $countryName->slug . '/university/' . $model->slug,
            ];
        }

        return $data;
    }

    public function actionSaSpecializationCardData()
    {
        $degree = Yii::$app->request->post('degree');
        $stream = Yii::$app->request->post('stream');
        $course = Yii::$app->request->post('course');
        $specialization = Yii::$app->request->post('specialization');
        $collegeId = Yii::$app->request->post('collegeId');

        $degreeId = SaDegree::find()->select(['id'])->where(['name' => $degree])->one();
        $streamId = SaStream::find()->select(['id'])->where(['name' => $stream])->one();
        $courseId = SaCourse::find()->select(['id'])->where(['name' => $course])->one();
        $specializationId = SaSpecialization::find()->select(['id'])->where(['name' => $specialization])->one();

        $courseDetails = (new StudyAbroadService)->getSaCourses((int) $collegeId, $degreeId->id, $streamId->id, $courseId->id, $specializationId->id);

        // Render the HTML view with the data
        $html = $this->renderPartial('/study-abroad/partials/_specialization_data', ['data' => $courseDetails]);

        // Return the HTML as part of the response
        return \yii\helpers\Json::encode([
            'status' => true,
            'html' => $html,
        ]);
    }

    /**
     * Finds College list on the basis of search parameter.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionLeadSaCountries()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;
        $term = trim($request->get('term', ''));
        $entity = $request->get('entity');

        $query = SaCountry::find()
            ->select(['sa_country.id', 'sa_country.name'])
            ->where(['like', 'sa_country.name', $term]);

        if ($entity == 1) { //current country
            $query->innerJoinWith(['saCity']);
        }
        $query->asArray();
        $countries = $query->all();

        if (empty($countries)) {
            return [];
        }

        foreach ($countries as $country) {
            $items[] = [
                'id' => $country['id'],
                'text' => $country['name'],
            ];
        }
        return $items;
    }

    /**
     * Finds College list on the basis of search parameter.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionLeadSaCities()
    {
        $items = [];
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $countries = SaCity::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->andWhere(['sa_country_id' => $request->get('country_id')])
            ->all();

        if (empty($countries)) {
            return [];
        }

        foreach ($countries as $country) {
            $items[] = [
                'id' => $country['id'],
                'text' => $country['name'],
            ];
        }
        return $items;
    }

    public function actionLeadSaDegree()
    {
        $items = [];
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $degrees = SaDegree::find()
            ->select(['id', 'name'])
            ->where(['like', 'name', trim($request->get('term'))])
            ->all();

        if (empty($degrees)) {
            return [];
        }

        foreach ($degrees as $degree) {
            $items[] = [
                'id' => $degree['id'],
                'text' => $degree['name'],
            ];
        }
        return $items;
    }

    public function actionDownloadableResource()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->get();

        $exam = Exam::findOne($request['examId']);
        if (!$exam) {
            return [];
        }
        $buckets = DataHelper::$ArticleNewsBucketDownloadableResource;

        if (!empty($request['primary_entity'])  && ($request['primary_entity'] == 'articles')) {
            $showWidget = LeadBucketTagging::find()
                ->innerJoin(LeadBucket::tableName(), 'lead_bucket.id = lead_bucket_tagging.bucket_id')
                ->where([
                    'lead_bucket_tagging.entity' => LeadBucket::LEAD_ENTITY_ARTICLE,
                    'lead_bucket_tagging.article_id' => $request['entity_id'],
                    'lead_bucket_tagging.status' => LeadBucketTagging::STATUS_ACTIVE,
                    'lead_bucket.entity_id' => LeadBucket::LEAD_ENTITY_NEWS_AND_ARTICLE,
                ])
                ->andWhere(['in', 'lead_bucket.bucket', $buckets])
                ->exists();

            if (!$showWidget) {
                return [];
            }
        }

        $mediaDownloadFiles = (new ExamService)->getDownloadableResource($request['examId'], $request['streamId'], $request['entity'], $request['primary_entity']);

        return $this->renderPartial('/exam/partials/_exam_downloadable_resource', [
            'downloadableResource' => $mediaDownloadFiles,
            'exam_name' => $exam->display_name,
            'exam_id' => $exam->id
        ]);
    }

    public function actionPopularColleges()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $stream = isset($request['entity']) && $request['entity'] == 'college-lisiting'
        ? Stream::find()->select(['id'])->where(['slug' => $request['streamSlug']])->scalar() : ($request['streamId'] ?? '');

        $state = isset($request['entity']) && $request['entity'] == 'college-lisiting'
        ? State::find()->select(['id'])->where(['slug' => $request['stateSlug']])->scalar() : ($request['stateId'] ?? '');

        if (isset($request['entity']) && $request['entity'] == 'college') {
            $colleges = PopularCollegeService::getCollegeEntityCards($request['collegeId'], $request['stateId']);
        } else {
            $levelId = empty($request['levelId']) ? null : $request['levelId'];
            $colleges = PopularCollegeService::getCollegeCards($stream, $levelId, $state, 5);
        }

        return $this->renderPartial('/layouts/_stream_location_college_cards', [
            'colleges' => $colleges['colleges'],
            'title' => 'Popular ' . (!empty($colleges['stream_name']) ? $colleges['stream_name'] : '') . ' Colleges Near You'
        ]);
    }


    public function actionLiveChatWidget()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        if (empty($request['entityId']) || empty($request['entity'])) {
            return [];
        }

        $liveChatGroup = UserService::getLiveChat($request['entityId'], $request['entity'], $request['primary_entity'] ?? '');

        if (empty($liveChatGroup)) {
            return [];
        }

        return $this->renderPartial('/partials/_live-chat-widget', [
            'liveChatGroup' => $liveChatGroup,
            'compact' => $request['entity'] == 'exam' || $request['entity'] == 'board' ? true : false
        ]);
    }

    public function actionGetQuesAns()
    {
        $articleID = Yii::$app->request->post('articleID');
        $tabContentId = (int)Yii::$app->request->post('tabContentId');
        $subtopicID = Yii::$app->request->post('subtopicID');
        $isInstruction = Yii::$app->request->post('isInstruction');

        $article = Article::find()->where(['id' => $articleID])->one();
        $articleSubTopic = ArticleSubpageSubsectionSubtopic::find()->where(['id' => $subtopicID])->one();
        $articleSubSectionTopic  = $this->articleService->getArticleSubpageSectionTopic($articleSubTopic->slug);
        // echo "<pre>"; print_r($articleSubSectionTopic); die;
        $isMobile = Yii::$app->devicedetect->isMobile();
        if (Yii::$app->user->isGuest) {
            $isLogin = false;
        } else {
            $isLogin = true;
        }
        //echo $isLogin; die;
        if (!empty($articleSubSectionTopic)) {
            $questionSets =  $this->articleService->getArticlePracticeDetailSubTopicQuesAjax($article, $articleSubSectionTopic->article_subpage_section_id, $articleSubSectionTopic->article_subpage_section_topic_id, $articleSubSectionTopic->id, $tabContentId, $isInstruction) ?? [];
            $returnResponse = $this->renderPartial('/article/partials/_ques_practice_set', [
                'questionSets' => $questionSets ?? [],
                'article' => $article,
                'subtopicID' => $subtopicID,
                'isMobile' => $isMobile,
                'isLogin' => $isLogin

            ]);
            return \yii\helpers\Json::encode([
                'status' => true,
                'html' => $returnResponse,
                'tabContentId' => $tabContentId,
                'isLogin' => $isLogin
            ]);
        } else {
        }
    }

    public function actionGetUserSession()
    {
        if (Yii::$app->user->isGuest) {
            return \yii\helpers\Json::encode([
                'status' => true,
                'isLogin' => 'No',

            ]);
        } else {
            return \yii\helpers\Json::encode([
                'status' => true,
                'isLogin' => 'Yes',

            ]);
        }
    }

    public function actionClpStateList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $states = (new ClpService)->getState($selectedIds, $q);

        foreach ($states as $state) {
            $results[] = [
                'id' => $state['id'],
                'text' => $state['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpCityList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $city = (new ClpService)->getCity($selectedIds, $q);

        foreach ($city as $value) {
            $results[] = [
                'id' => $value['id'],
                'text' => $value['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpStudyModeList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $studyMode = (new ClpService)->getStudyMode($selectedIds, $q);

        foreach ($studyMode as $value) {
            $results[] = [
                'id' => $value['value'],
                'text' => $value['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpCourseList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];
        $collegeId = $request['college_id'] ?? null;
        $templateId = $request['template_id'] ?? null;

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $courses = (new ClpService)->getCourses($selectedIds, $templateId, $collegeId, $q);

        foreach ($courses as $course) {
            $results[] = [
                'id' => $course['id'],
                'text' => $course['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpStreamList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];
        $collegeId = $request['college_id'] ?? null;
        $templateId = $request['template_id'] ?? null;

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $streams = (new ClpService)->getStreams($selectedIds, null, $templateId, $collegeId, $q);

        foreach ($streams as $stream) {
            $results[] = [
                'id' => $stream['id'],
                'text' => $stream['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpDegreeList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];
        $collegeId = $request['college_id'] ?? null;
        $templateId = $request['template_id'] ?? null;
        $streamId = $request['stream_id'] ?? null;

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $degrees = (new ClpService)->getDegrees($selectedIds, null, $templateId, $collegeId, $q, $streamId);

        foreach ($degrees as $degree) {
            $results[] = [
                'id' => $degree['id'],
                'text' => $degree['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpProgramList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];
        $collegeId = $request['college_id'] ?? null;
        $templateId = $request['template_id'] ?? null;

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $programs = (new ClpService)->getProgramsList($selectedIds, $templateId, $collegeId, $q);

        foreach ($programs as $program) {
            $results[] = [
                'id' => $program['id'],
                'text' => $program['name']
            ];
        }

        return ['results' => $results];
    }

    public function actionClpCampusList()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $request = Yii::$app->request->post();

        $q = $request['q'] ?? '';
        $ids = !empty($request['ids']) ? explode(',', $request['ids']) : [];

        $results = [];

        $selectedIds = is_array($ids) ? $ids : [];

        $campus = (new ClpService)->getCampus($selectedIds, $q);

        foreach ($campus as $value) {
            $results[] = [
                'id' => $value['id'],
                'text' => $value['name']
            ];
        }

        return ['results' => $results];
    }


    public function actionGetCutoffCategory()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;
        $request = \Yii::$app->request;

        $items = [];

        $cutOffCategory = CutoffCategory::find()
            ->where(['like', 'name', trim($request->get('term'))])
            ->all();

        foreach ($cutOffCategory as $category) {
            $items[] = [
                'id' => $category['id'],
                'text' => $category['name'],
            ];
        }

        return $items;
    }

    public function actionExamWidgetSection()
    {
        $request = Yii::$app->request->get();

        $examDates = (new ExamService)->getDate($request['examId']);
        $exam = Exam::findOne($request['examId']);

        return $this->renderPartial('/exam/partials/_exam_widget', [
            'exam' => $exam,
            'dates' => $examDates
        ]);
    }

    public function actionStreamList($query, $entity = '')
    {
        return $this->prepareSelect2List(Stream::class, $query, $entity);
    }

    public function actionDegreeList($query)
    {
        return $this->prepareSelect2List(Degree::class, $query);
    }

    private function prepareSelect2List(string $modelClass, string $query, string $entity = ''): array
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $models = $modelClass::find()
            ->select(['id', 'name', 'slug'])
            ->where(['like', 'name', $query])
            ->limit(20)
            ->asArray()->all();

        $results = [];
        foreach ($models as $model) {
            if (!empty($entity) && isset($model[$entity])) {
                $results[] = ['id' => $model[$entity], 'text' => $model[$entity]];
            } else {
                $results[] = ['id' => $model['id'], 'text' => $model['name']];
            }
        }

        return ['results' => $results];
    }

    // public function actionGeneratePdfCutOff()
    // {
    //     Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

    //     $request = Yii::$app->request->post();
    //     $latestValue = $request['latestValue'] ?? '';
    //     $programId = $request['programId'] ?? '';
    //     $type = $request['type'] ?? '';

    //     if (empty($latestValue) || empty($programId) || empty($type)) {
    //         return ['status' => false, 'message' => 'Missing required parameters'];
    //     }

    //     // Get all eligible cutoffs sorted by round descending
    //     $allCutoffs = CutOff::find()
    //         ->where(['program_id' => $programId])
    //         ->andWhere(['<=', $type, $latestValue])
    //         ->orderBy(['round' => SORT_DESC])
    //         ->all();

    //     if (empty($allCutoffs)) {
    //         return ['status' => false, 'message' => 'No matching cutoffs found'];
    //     }

    //     // Group by college => [round => [cutoffs]]
    //     $groupedCutoffs = [];
    //     foreach ($allCutoffs as $cutoff) {
    //         $collegeId = $cutoff->college_id;
    //         $round = $cutoff->round;

    //         $groupedCutoffs[$collegeId][$round][] = $cutoff;
    //     }

    //     // Limit to 10 colleges and latest 3 rounds per college
    //     $cutOffData = [];
    //     $collegeCount = 0;
    //     foreach ($groupedCutoffs as $rounds) {
    //         if ($collegeCount++ >= 10) {
    //             break;
    //         }

    //         krsort($rounds);
    //         $latest3 = array_slice($rounds, 0, 3, true);

    //         foreach ($latest3 as $cutoffs) {
    //             $cutOffData = array_merge($cutOffData, $cutoffs);
    //         }
    //     }

    //     if (empty($cutOffData)) {
    //         return ['status' => false, 'message' => 'Insufficient data after filtering'];
    //     }

    //     $htmlContent = $this->renderPartial('/college/partials/_generate-pdf-cutoff', [
    //         'cutOffData' => $cutOffData,
    //         'latestValue' => $latestValue,
    //         'type' => $type,
    //     ]);

    //     // ✅ your generatePdf method returns a JSON string, so decode it
    //     $json = $this->generatePdf('cut-off', $htmlContent);

    //     return Json::decode($json);
    // }

    public function actionGeneratePdfCutOff()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $request = Yii::$app->request->post();
        $latestValue = $request['latestValue'] ?? '';
        $programId = $request['programId'] ?? '';
        $collegeId = $request['collegeId'];
        $type = $request['type'] ?? '';
        $programName = $request['programName'] ?? '';
        $courseName = $request['courseName'] ?? '';
        $category = $request['categorySelected'] ?? '';

        if (empty($latestValue) || empty($programId) || empty($type)) {
            return ['status' => false, 'message' => 'Missing required parameters'];
        }

        $typeMap = [
            'closing_rank' => ['max' => 'opening_rank', 'min' => 'closing_rank'],
            'percentile' => ['max' => 'percentile', 'min' => 'percentile'],
            'closing_score' => ['max' => 'closing_score', 'min' => 'closing_score']
        ];

        if (!isset($typeMap[$type])) {
            return ['status' => false, 'message' => 'Invalid type provided'];
        }

        $maxField = $typeMap[$type]['max'];
        $minField = $typeMap[$type]['min'];

        $subQuery = (new \yii\db\Query())
            ->select([
                'college_id',
                'round',
                'MAX(' . $maxField . ') as max_val',
                'MIN(' . $minField . ') as min_val',
                'program_id',
            ])
            ->from('cutoff_detail')
            ->where([
                'and',
                ['program_id' => $programId],
                ['<=', $type, $latestValue],
            ])
            ->groupBy(['college_id', 'round'])
            ->orderBy(['college_id' => SORT_ASC, 'round' => SORT_DESC]);

        $allResults = $subQuery->all();
        $collegeIds = array_unique(array_column($allResults, 'college_id'));

        if (empty($allResults)) {
            return ['status' => false, 'message' => 'No matching cutoffs found'];
        }

        $collegeMap = ArrayHelper::map(
            \common\models\College::find()->select(['id', 'name'])->where(['id' => $collegeIds])->asArray()->all(),
            'id',
            'name'
        );

        // Structure output
        $grouped = [];
        foreach ($allResults as $row) {
            $round = $row['round'];

            $collegeId = (int) $row['college_id'];

            if (!isset($collegeMap[$collegeId])) {
                continue; // or log the issue for debugging
            }

            $collegeName = $collegeMap[$collegeId];

            $opening = $row['max_val'] ?? null;
            $closing = $row['min_val'] ?? null;

            if (!isset($grouped[$collegeName])) {
                $grouped[$collegeName] = ['rounds' => []];
            }

            $grouped[$collegeName]['rounds'][$round] = [
                'opening' => $opening,
                'closing' => $closing,
            ];
        }

        // Limit to top 10 colleges with latest 3 rounds
        $cutOffData = [];
        $count = 0;
        foreach ($grouped as $collegeName => $collegeData) {
            if ($count++ >= 10) {
                break;
            }

            $rounds = $collegeData['rounds'];
            krsort($rounds); // Sort rounds in descending order
            $cutOffData[$collegeName] = ['rounds' => array_slice($rounds, 0, 3, true)];
        }

        $otherProgramsQuery = (new \yii\db\Query())
            ->select([
                'program_name',
                'round',
                'MAX(' . $maxField . ') as max_val',
                'MIN(' . $minField . ') as min_val',
            ])
            ->from('cutoff_detail')
            ->where([
                'college_id' => $request['collegeId']
            ])
            ->andWhere(['<>', 'program_name', $programName]) // Exclude selected one
            ->groupBy(['program_name', 'round'])
            ->orderBy(['program_name' => SORT_ASC, 'round' => SORT_DESC]);

        $otherProgramResults = $otherProgramsQuery->all();

        $otherPrograms = [];
        $programCount = 0;
        $maxPrograms = 10;

        foreach ($otherProgramResults as $row) {
            $normalizedName = trim($row['program_name']);

            $round = $row['round'];

            if (!isset($otherPrograms[$normalizedName]) && $programCount >= $maxPrograms) {
                continue;
            }

            if (!isset($otherPrograms[$normalizedName])) {
                $otherPrograms[$normalizedName] = [];
                $programCount++;
            }

            // Store only unique rounds
            if (!isset($otherPrograms[$normalizedName][$round])) {
                $otherPrograms[$normalizedName][$round] = [
                    'opening' => $row['max_val'],
                    'closing' => $row['min_val'],
                ];
            }
        }

        // Keep only the latest 3 rounds per program
        foreach ($otherPrograms as &$rounds) {
            krsort($rounds);
            $rounds = array_slice($rounds, 0, 3, true);
        }
        unset($rounds);

        $htmlContent = $this->renderPartial('/college/partials/_generate-pdf-cutoff', [
            'cutOffData' => $cutOffData,
            'latestValue' => $latestValue,
            'type' => $type,
            'otherPrograms' => $otherPrograms,
            'collegeName' => $collegeMap[$collegeId] ?? '',
            'courseName' => $courseName,
            'programName' => $programName,
            'category' =>  $category ?? 'All India/ General',
        ]);

        $json = $this->generatePdf('cut-off', $htmlContent);
        return Json::decode($json);
    }

    // public static function actionSavePollResponse()
    // {
    //     Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

    //     $pollId = Yii::$app->request->post('poll_id');
    //     $newsId = Yii::$app->request->post('news_id');
    //     $userId = Yii::$app->request->post('user_id') ?: null;
    //     $selectedOption = Yii::$app->request->post('selected_option');

    //     $model = PollResponse::find()
    //         ->where([
    //             'poll_id' => $pollId,
    //             'news_id' => $newsId,
    //             'user_id' => $userId,
    //         ])
    //         ->one();

    //     if (!$model) {
    //         $model = new PollResponse();
    //         $model->poll_id = $pollId;
    //         $model->news_id = $newsId;
    //         $model->user_id = $userId;
    //         $model->created_at = date('Y-m-d H:i:s');
    //         $model->selected_option = $selectedOption;
    //         $model->submitted_at = date('Y-m-d H:i:s');
    //         $model->ip_address = Yii::$app->request->userIP;
    //         $model->session_id = Yii::$app->session->id;
    //     }

    //     $model->updated_at = date('Y-m-d H:i:s');

    //     if ($model->save()) {
    //         return ['status' => 'success', 'message' => 'Thanks! Your vote has been recorded.'];
    //     } else {
    //         return ['status' => 'error', 'errors' => $model->errors];
    //     }
    // }

    public static function actionSavePollResponse()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $pollId = Yii::$app->request->post('poll_id');
        $entity = Yii::$app->request->post('entity');
        $entityId = Yii::$app->request->post('entity_id');
        $userId = Yii::$app->request->post('user_id') ?: null;
        $selectedOption = Yii::$app->request->post('selected_option');

        $model = PollResponse::find()
            ->where([
                'poll_id' => $pollId,
                'entity' => $entity,
                'entity_id' => $entityId,
                'user_id' => $userId,
            ])
            ->one();

        if (!$model) {
            $model = new PollResponse();
            $model->poll_id = $pollId;
            $model->entity = $entity;
            $model->entity_id = $entityId;
            $model->user_id = $userId;
            $model->created_at = date('Y-m-d H:i:s');
            $model->selected_option = $selectedOption;
            $model->submitted_at = date('Y-m-d H:i:s');
            $model->ip_address = Yii::$app->request->userIP;
            $model->session_id = Yii::$app->session->id;
        }

        $model->updated_at = date('Y-m-d H:i:s');

        if ($model->save()) {
            return ['status' => 'success', 'message' => 'Thanks! Your vote has been recorded.'];
        } else {
            return ['status' => 'error', 'errors' => $model->errors];
        }
    }
}
