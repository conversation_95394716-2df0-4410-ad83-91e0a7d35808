<?php
$base_url = isset($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off' ? 'https' : 'http';
$base_url .= '://' . $_SERVER['HTTP_HOST'] . '/';

return [
    'adminEmail' => '<EMAIL>',
    'supportEmail' => '<EMAIL>',
    'senderEmail' => '<EMAIL>',
    'senderName' => 'Example.com mailer',
    'user.passwordResetTokenExpire' => 3600,
    'user.passwordMinLength' => 8,
    'tansEmail' => ['<EMAIL>' => 'GetMyUni'],
    'gmu_image_api' => 'http://getmyuni.com/imageeditor/upload',
    'siteUrl' => !empty($base_url)  ? $base_url : 'https://www.getmyuni.com/',
    'azureUrl' => 'https://getmyuni.azureedge.net/',
    'assetsUrl' => 'https://getmyuni.com/assets/',
    'gmuLogo' => 'https://getmyuni.azureedge.net/assets/images/logo_squre.png',
    'newsBaseURI' => 'https://assets.getmyuni.com',
    'availableLocales' => [
        'en-US' => 'English (US)',
    ],
    'Year' => '2025',
    'previousYear' => '2022',
    'aws' => [
        's3' => [
            'key' => '********************',
            'secret' => 'NEt1qpfubd9DU+EbPz+dh1hz9Ahw0onlIwQPXnPP',
            'bucket' => 'getmyuni-assets',
            'url' => 'https://getmyuni-assets.s3.ap-south-1.amazonaws.com',
            'region' => 'ap-south-1'
        ],
    ],
    'bdeRole' => 'BDM',
    'tlRole' => 'ZonalHead',
    'BDHead' => 'BDHead',
    'articleExport' => 'ArticleExport',
    'LeadTransferPermission' => 'LeadTransferPermission',
    'Initial Call' => 'Initial Call',
    'Onboarded' => 'Onboarded',
    'allLeadReportingRole' => 'AllLeadReporting',
    'cssPath' => (YII_ENV == 'prod') ? '/yas/css/version2/min/' : '/yas/css/version2/',
    'jsPath' => (YII_ENV == 'prod') ? '/yas/js/version2/min/' : '/yas/js/version2/',
    'GetGisUrl' => 'https://getgis.org/',
    'adminContact' => '+91-8512093920',
    'supportEmail' => '<EMAIL>',
    'supportEmailUser' => '<EMAIL>',

    // 'AZURE_ENDPOINT' => "https://saarthi-ai394538790176.openai.azure.com/",
    'AZURE_ENDPOINT' => 'https://saarthi-ai394538790176.cognitiveservices.azure.com/',

    'AZURE_API_KEY' => '********************************',
    'AZURE_API_VERSION' => '2024-12-01-preview',
];
