<?php

namespace common\models;

use common\event\SitemapEvent;
use common\event\SitemapEventNew;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\behaviors\SluggableBehavior;
use common\services\CacheClearService;
use common\services\UserService;

/**
 * This is the model class for table "board_content".
 *
 * @property int $id
 * @property int|null $board_id
 * @property int|null $author_id
 * @property string|null $page
 * @property string|null $page_slug
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $short_description
 * @property string|null $content
 * @property string|null $short_summary
 * @property string|null $cover_image
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $author
 * @property Board $board
 */
class BoardContent extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_DRAFT = 2;

    const SCENARIO_IMPORTER = 'importer';

    const IS_FREELANCER_NO = 0;
    const IS_FREELANCER_YES = 1;

    public $preview;
    public $skipAfterSave = false;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'board_content';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'page',
                'slugAttribute' => 'page_slug',
                'immutable' => true
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['board_id'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['board_id', 'author_id', 'status', 'lang_code'], 'integer'],
            [['content', 'editor_remark', 'short_summary'], 'string'],
            [['created_at', 'updated_at', 'parent_id'], 'safe'],
            [['page', 'page_slug', 'h1', 'meta_title', 'old_page_slug'], 'string', 'max' => 255],
            [['meta_description'], 'string', 'max' => 350],
            [['short_description'], 'string', 'max' => 300],
            [['cover_image'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'webp'],
            [['cover_image'], 'image', 'maxWidth' => '1200', 'maxHeight' => '667', 'maxSize' => 1024 * 100, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 100kb'],
            [['author_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['author_id' => 'id']],
            [['board_id'], 'exist', 'skipOnError' => true, 'targetClass' => Board::className(), 'targetAttribute' => ['board_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'board_id' => 'Board ID',
            'author_id' => 'Author ID',
            'parent_id' => 'Child',
            'page' => 'Page',
            'page_slug' => 'Page Slug',
            'old_page_slug' => 'Old Page Slug',
            'h1' => 'H1',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'short_description' => 'Short Description',
            'lang_code' => 'Language Code',
            'content' => 'Content',
            'short_summary' => 'Short Summary',
            'cover_image' => 'Schema Image',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id'])->where(['not', ['user.status' => User::STATUS_DELETED]])->select(['id', 'name', 'slug']);
    }

    /**
     * Gets query for [[Board]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getBoard()
    {
        return $this->hasOne(Board::className(), ['id' => 'board_id'])->where(['board.status' => Board::STATUS_ACTIVE]);
    }

    /**
     * Gets query for [[Board]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getParent()
    {
        return $this->hasOne(BoardContent::className(), ['id' => 'parent_id']);
    }


    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getBackendauthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
    }

    public function getDefaultuser()
    {
        return UserService::getDefaultUserData();
    }

    // update sitemap collections
    public function afterSave($insert, $changedAttributes)
    {
        if ($this->skipAfterSave) {
            return;
        }

        (new SitemapEventNew())->generateBoardSitemap($this->board_id, $this->page_slug);

        // CacheClearService::entityContent(Board::ENTITY_BOARD, $this->parent_id, $this->page_slug, $this->board->slug, $this->board_id, '', $this->lang_code);

        return parent::afterSave($insert, $changedAttributes);
    }
}
