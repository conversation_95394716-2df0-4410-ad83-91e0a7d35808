<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "quiz".
 *
 * @property int $id
 * @property int $entity_id
 * @property string $entity
 * @property string $lang_code
 * @property string $question
 * @property array|null $options
 * @property string|null $correct_answer
 * @property int|null $sort_order
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property NewsSubdomain $news
 */
class Quiz extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_DELETED = 2;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'quiz';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity_id', 'entity', 'lang_code', 'question'], 'required'],
            [['entity_id', 'sort_order', 'status'], 'integer'],
            [['question'], 'string'],
            [['options'], 'safe'],
            [['created_at', 'updated_at'], 'safe'],
            [['correct_answer'], 'string', 'max' => 255],
            [['entity'], 'string', 'max' => 50],
            [['lang_code'], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity_id' => 'Entity ID',
            'entity' => 'Entity Type',
            'lang_code' => 'Language Code',
            'question' => 'Question',
            'options' => 'Options',
            'correct_answer' => 'Correct Answer',
            'sort_order' => 'Sort Order',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNews()
    {
        return $this->hasOne(NewsSubdomain::class, ['id' => 'entity_id']);
    }

    /**
     * Handle JSON encoding/decoding for options field
     */
    public function afterFind()
    {
        parent::afterFind();
        if (is_string($this->options)) {
            $this->options = json_decode($this->options, true);
        }
    }

    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if (is_array($this->options)) {
                $this->options = json_encode($this->options);
            }
            return true;
        }
        return false;
    }
}
