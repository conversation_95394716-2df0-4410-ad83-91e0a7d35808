<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "quick_fact".
 *
 * @property int $id
 * @property string $entity
 * @property int $entity_id
 * @property string|null $title
 * @property string|null $h1
 * @property string $content
 * @property int $source_type
 * @property int $status
 * @property string|null $published_at
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class QuickFact extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */

    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    const SOURCE_TYPE_MANUAL = 0;
    const SOURCE_TYPE_AUTO = 1;

    public static $fields = [
        'article' => ['id', 'title'],
        'exam' => ['id', 'name'],
        'board' => ['id', 'display_name'],
        'college' => ['id', 'name'],
        'filter' => ['id', 'name'],
        'course' => ['id', 'name'],
        'career' => ['id', 'name'],
        'ncert' => ['id', 'title'],
        'news' => ['id', 'name'],
    ];

    public static function tableName()
    {
        return 'quick_fact';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity', 'entity_id', 'content'], 'required'],
            [['entity_id', 'source_type', 'status'], 'integer'],
            [['content'], 'string'],
            [['published_at', 'created_at', 'updated_at'], 'safe'],
            [['entity', 'title', 'h1'], 'string', 'max' => 255],
        ];
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity' => 'Entity',
            'entity_id' => 'Entity ID',
            'title' => 'Title',
            'h1' => 'H1',
            'content' => 'Content',
            'source_type' => 'Source Type',
            'status' => 'Status',
            'published_at' => 'Published At',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    public function getTitle()
    {
        $entity = strtolower($this->entity);

        $mapping = [
            'articles' => 'article',
            'boards' => 'board',
            'colleges' => 'college',
            'exams' => 'exam',
            'filters' => 'filter',
            'courses' => 'course',
            'careers' => 'career',
            'ncerts' => 'ncert',
            'newses' => 'news', // rare, but safe
        ];

        if (isset($mapping[$entity])) {
            $entity = $mapping[$entity];
        }

        if (!isset(self::$fields[$entity])) {
            Yii::warning("Undefined entity '{$this->entity}' in QuickFact::getTitle()", __METHOD__);
            return '';
        }

        list($id, $name) = self::$fields[$entity];
        return !empty($this->$entity->$name) ? $this->$entity->$name : '';
    }
}
