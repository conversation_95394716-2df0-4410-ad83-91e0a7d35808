<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "auto_shorts".
 *
 * @property int $id
 * @property int $entity_id
 * @property string $entity
 * @property string $lang_code
 * @property string $content
 * @property int|null $sort_order
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property NewsSubdomain $news
 */
class AutoShorts extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DELETED = 2;
    
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => ['updated_at'],
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'auto_shorts';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['entity_id', 'entity', 'lang_code', 'content'], 'required'],
            [['entity_id', 'sort_order', 'status'], 'integer'],
            [['content'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['entity'], 'string', 'max' => 50],
            [['lang_code'], 'string', 'max' => 10],
            [['entity_id'], 'exist', 'skipOnError' => true, 'targetClass' => NewsSubdomain::class, 'targetAttribute' => ['entity_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'entity_id' => 'Entity ID',
            'entity' => 'Entity Type',
            'lang_code' => 'Language Code',
            'content' => 'Content',
            'sort_order' => 'Sort Order',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNews()
    {
        return $this->hasOne(NewsSubdomain::class, ['id' => 'entity_id']);
    }
}
