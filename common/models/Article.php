<?php

namespace common\models;

use Yii;
use yii\db\ActiveRecord;
use common\behaviors\Taggable;
use common\event\SitemapEvent;
use common\event\SitemapEventNew;
use common\models\CategoryTranslation;
use common\models\ArticleTranslationCld;
use common\services\ElasticSearchService;
use common\models\Tag;
use common\services\MysqlSearchService;
use Exception;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use common\helpers\DataHelper;
use common\services\v2\NewsService;
use common\services\EmailService;
use common\services\CacheClearService;
use common\models\LeadBucketTagging;
use common\services\UserService;
use yii\db\Query;
use GuzzleHttp\Client as GuzzleHttpClient;
use yii\helpers\Inflector;

/**
 * This is the model class for table "article".
 *
 * @property int $id
 * @property int $author_id
 * @property int $category_id
 * @property string|null $country_slug
 * @property string $title
 * @property string $slug
 * @property string|null $cover_image
 * @property string|null $description
 * @property string|null $short_summary
 * @property string|null $h1
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property int $view_count
 * @property int $is_popular
 * @property int $position
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $author
 * @property Category $category
 * @property ArticleTag[] $articleTags
 * @property Tag[] $tags
 */
class Article extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DRAFT = 2;

    const POPULAR_YES   = 1;
    const POPULAR_NO    = 0;

    const SCENARIO_IMPORTER = 'importer';

    const ENTITY_ARTICLE = 'articles';
    const ENTITY_ARTICLE_URL = 'article';
    const ENTITY_ARTICLE_CATEGORY = 'article_category';
    const ENTITY_STUDY_ABROAD = 'study-abroad';
    const ENTITY_REGISTER = 'article-register';
    const AUTHORIZATION_KEY = 'Token 4e502eb808d2bc435b362f1d592ec9fd3dad89dc';
    const TRANSLATION_URL = 'http://************/translate/';
    const CALLBACK_URL = 'http://api.getmyuni.com/v1/collegedekho/article-translation-cld';

    const LANG_ENGLISH = 1;
    const LANG_HINDI = 2;

    const IS_FREELANCER_NO = 0;
    const IS_FREELANCER_YES = 1;

    public $slug_title;
    public $tagNames;
    public $preview;
    public $bucket_id;
    public $skipAfterSave = false;

    public function behaviors()
    {
        return [
            [
                'class' => Taggable::class,
                'name' => 'id',
            ],
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'title',
                'immutable' => true
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'article';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        
        return [
            [['category_id',  'view_count', 'meta_description', 'stream_id', 'highest_qualification', 'bucket_id'], 'required', 'when' => function ($model) {
                return $model->is_freelancer == Article::IS_FREELANCER_NO;
            }, 'whenClient' => "function (attribute, value) {
                if($('#update-validation').val()==1){
                    return true;
                }
                return $('#article-is_freelancer').val() == '0';
            }",'except' => self::SCENARIO_IMPORTER],
            [['author_id' , 'title'], 'required','except' => self::SCENARIO_IMPORTER],
            ['country_slug', 'required', 'when' => function ($model) {
                return $model->entity == Article::ENTITY_STUDY_ABROAD;
            }],
            [['author_id', 'category_id', 'view_count', 'is_popular', 'position', 'status', 'lang_code', 'is_freelancer'], 'integer'],
            [['description', 'country_slug', 'editor_remark', 'display_name', 'short_summary'], 'string'],
            [['created_at', 'updated_at', 'tagNames', 'entity', 'entity_id', 'published_at', 'scheduled_at', 'created_by', 'updated_by'], 'safe'],
            [['title', 'slug', 'h1', 'meta_title'], 'string', 'max' => 255],
            [['cover_image'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'webp'],
            [['cover_image'], 'image', 'maxWidth' => '1200', 'maxHeight' => '667', 'maxSize' => 1024 * 100, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 100kb'],
            [['audio'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'mp3', 'maxSize' => 1024 * 1024 * 10, 'message' => 'Audio file only support MP3'],
            [['slug', 'lang_code'], 'unique', 'targetAttribute' => ['slug', 'lang_code']],
            [['author_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['author_id' => 'id']],
            [['category_id'], 'exist', 'skipOnError' => true, 'targetClass' => Category::className(), 'targetAttribute' => ['category_id' => 'id']],
            [['is_popular'], 'required', 'on' => 'update'],
            // [['status'], 'default', 'value' => self::STATUS_DRAFT],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Article Id',
            'author_id' => 'Author',
            'category_id' => 'Category',
            'country_slug' => 'Country Slug',
            'category_name' => 'Category Name',
            'title' => 'Title',
            'display_name' => 'Display Name',
            'slug' => 'Slug',
            'cover_image' => 'Cover Image',
            'description' => 'Description',
            'short_summary' => 'Short Summary',
            'h1' => 'H1',
            'lang_code' => 'Language Code',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'view_count' => 'View Count',
            'is_popular' => 'Is Popular',
            'position' => 'Position',
            'status' => 'Status',
            'is_freelancer' => 'Is Freelancer',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'published_at' => 'Published At',
            'scheduled_at' => 'Scheduled At',
            'stream_id' => 'Stream',
            'highest_qualification' => 'Highest Qualification'
        ];
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id'])->where(['not', ['user.status' => User::STATUS_DELETED]]);
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getBackendauthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
    }

    public function getDefaultuser()
    {
        return UserService::getDefaultUserData();
    }

    /**
     * Gets query for [[Author Translation]].
     */
    public function getTransAuthor()
    {
        return $this->hasOne(UserTranslation::className(), ['tag_user_id' => 'author_id']);
    }

    /**
     * Gets query for [[Category]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CategoryQuery
     */
    public function getCategory()
    {
        return $this->hasOne(Category::className(), ['id' => 'category_id']);
    }

    /**
     * Gets query for [[Category Translation]].
     */
    public function getTranslatedCategory()
    {
        return  $this->hasOne(CategoryTranslation::className(), ['category_id' => 'category_id']);
    }

    /**
     * Gets query for [[ArticleTags]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ArticleTagQuery
     */
    // public function getArticleTags()
    // {
    //     return $this->hasMany(ArticleTag::className(), ['article_id' => 'id']);
    // }

    /**
     * Gets query for [[Tags]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\TagQuery
     */
    public function getTags()
    {
        return $this->hasMany(Tag::className(), ['id' => 'tag_id'])->viaTable('article_tag', ['article_id' => 'id']);
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getNews()
    {
        $tableName = NewsService::getInstance('article_news', Yii::$app->controller->id);
        return $this->hasMany(News::className(), ['id' => 'news_id'])->viaTable($tableName, ['article_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => News::STATUS_ACTIVE]);
    }

    public function saveNews(array $newsIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(News::class . ' id is required');
        }

        if (empty($newsIds) || !$this->isNewRecord) {
            $this->unlinkAll('news', true);
        }

        foreach ($newsIds as $newsId) {
            $newsModel = News::findOne($newsId);
            if (!$newsModel) {
                continue;
            }

            $this->link('news', $newsModel);
        }
    }

    public function getCollege()
    {
        return $this->hasMany(College::className(), ['id' => 'college_id'])->viaTable('article_college', ['article_id' => 'id'])->orderBy(['position' => SORT_DESC]);
    }

    public function saveCollege(array $collegeIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(College::class . ' id is required');
        }

        if (empty($collegeIds) || !$this->isNewRecord) {
            $this->unlinkAll('college', true);
        }

        foreach ($collegeIds as $collegeId) {
            $collegeModel = College::findOne($collegeId);
            if (!$collegeModel) {
                continue;
            }

            $this->link('college', $collegeModel);
        }
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\NewsQuery
     */
    public function getExam()
    {
        return $this->hasMany(Exam::className(), ['id' => 'exam_id'])->viaTable('article_exam', ['article_id' => 'id'])->orderBy(['position' => SORT_DESC]);
    }

    public function saveExam(array $examIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Exam::class . ' id is required');
        }

        if (empty($examIds) || !$this->isNewRecord) {
            $this->unlinkAll('exam', true);
        }

        foreach ($examIds as $examId) {
            $examModel = Exam::findOne($examId);
            if (!$examModel) {
                continue;
            }

            $this->link('exam', $examModel);
        }
    }

    public function getBoard()
    {
        return $this->hasMany(Board::className(), ['id' => 'board_id'])->viaTable('article_board', ['article_id' => 'id'])
            ->where(['status' => Board::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveBoard(array $boardIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Board::class . ' id is required');
        }

        if (empty($boardIds) || !$this->isNewRecord) {
            $this->unlinkAll('board', true);
        }

        foreach ($boardIds as $boardId) {
            $boardModel = Board::findOne($boardId);
            if (!$boardModel) {
                continue;
            }

            $this->link('board', $boardModel);
        }
    }

    public function getArticle()
    {
        return $this->hasMany(Article::className(), ['id' => 'article_id'])->viaTable('article_article', ['article_id_map' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveArticle(array $articleIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Article::class . ' id is required');
        }

        if (empty($articleIds) || !$this->isNewRecord) {
            $this->unlinkAll('article', true);
        }

        foreach ($articleIds as $articleId) {
            $articleModel = Article::findOne($articleId);
            if (!$articleModel) {
                continue;
            }

            $this->link('article', $articleModel);
        }
    }

    public function getCourse()
    {
        return $this->hasMany(Course::className(), ['id' => 'course_id'])->viaTable('article_course', ['article_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveCourse(array $courseIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Course::class . ' id is required');
        }

        if (empty($courseIds) || !$this->isNewRecord) {
            $this->unlinkAll('course', true);
        }

        foreach ($courseIds as $courseId) {
            $courseModel = Course::findOne($courseId);
            if (!$courseModel) {
                continue;
            }

            $this->link('course', $courseModel);
        }
    }

    /**
     * Gets query for [[Active Translation Article]].
     */
    public function getActiveTranslation()
    {
        $query1 = $this->hasMany(Article::className(), ['id' => 'translation_tag_id'])->viaTable('article_translation', ['article_id' => 'id']);
        $query2 =  $this->hasMany(Article::className(), ['id' => 'article_id'])->viaTable('article_translation', ['translation_tag_id' => 'id']);
        return (new yii\db\Query())
            ->select('*')
            ->from(
                $query1->union($query2)
            )
            ->where(['status' => self::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();
    }

    /**
     * Gets query for [[Translation Article]].
     */
    public function getTranslation()
    {
        return $this->hasMany(Article::className(), ['id' => 'translation_tag_id'])->viaTable('article_translation', ['article_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    /**
     * Post query for [[Article Translation]].
     */
    public function saveTranslation(array $translationIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Article::class . ' id is required');
        }

        if (empty($translationIds) || !$this->isNewRecord) {
            $this->unlinkAll('translation', true);
        }

        foreach ($translationIds as $translationId) {
            $translationModel = Article::findOne($translationId);
            if (!$translationModel) {
                continue;
            }
            $this->link('translation', $translationModel);
        }
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\ArticleQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\ArticleQuery(get_called_class());
    }

    // update Article Sitemap collections
    public function afterSave($insert, $changedAttributes)
    {
        if ($this->skipAfterSave) {
            return;
        }

        // (new SitemapEvent())->updateArticleSitemap($this->slug);
        // CacheClearService::entityContent(Article::ENTITY_ARTICLE, '', '', $this->slug, $this->id, '', $this->lang_code);
        if (!empty($this->id)) {
            LeadBucketTagging::taggingNewsArticle(Article::ENTITY_ARTICLE, $this->id, $this->bucket_id);
            // $this->sendVernacArticleMail($this->id);
        }

        $lang_code = array_search($this->lang_code, DataHelper::$languageCode);

        if ($lang_code == 'en') {
            //$this->getArticleTranslationCLD($this);
        }
        (new SitemapEventNew())->updateArticleSitemap($this->id);
        return parent::afterSave($insert, $changedAttributes);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        } else {
            if (!$insert && $this->status == Article::STATUS_DRAFT && $this->isAttributeChanged('title') && $this->lang_code == 1) {
                $slug = Inflector::slug($this->title, '-');
                $article = self::find()->where(['slug' => $slug])->andWhere(['!=', 'id', $this->id])->exists();
                if ($article) {
                    $this->addError('slug', 'Slug already exists.');
                    return false; // Prevent saving if there's an error
                }
                $this->slug = $slug;
            }
            return true;
        }
    }

    public function getStream()
    {
        return $this->hasOne(Stream::className(), ['id' => 'stream_id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function getDegree()
    {
        return $this->hasOne(Degree::className(), ['id' => 'highest_qualification'])->orderBy(['updated_at' => SORT_DESC]);
    }    /// supage
    public function getArticleSubpage()
    {
        return $this->hasOne(ArticleSubpage::className(), ['article_id' => 'id']);
    }

    public function getArticleSection()
    {
        return $this->hasMany(ArticleSubpageSection::className(), ['article_id' => 'id'])->select(['name','id','slug','title','meta_description','h1','description','status'])->where(['status'=>ArticleSubpageSection::STATUS_ACTIVE]);
    }

    public function getArticleExamSection()
    {
        return $this->hasOne(ArticleSubpageSection::className(), ['article_id' => 'id'])->select(['name','id','slug','title','meta_description','h1','status'])->where(['status'=>ArticleSubpageSection::STATUS_ACTIVE]);
    }

    public function sendVernacArticleMail($article_id)
    {
        $query  = new Query();
        $query->select(['article_id'])->from('article_translation')
            ->where('translation_tag_id=:id', ['id' => $article_id]);
        $result  = $query->all();

        if (!empty($result)) {
            $items = [];
            foreach ($result as $article) {
                $translateArticle = Article::findOne($article['article_id']);
                $items[] = [
                    'title' =>  !empty($translateArticle) ? $translateArticle->title : '',
                    'slug' => !empty($translateArticle) ? $translateArticle->slug : ''
                ];
            }
            if (!empty($items)) {
                EmailService::sendEmail(
                    EmailService::articleTranslationList(),
                    'Article Translation List',
                    [
                        'dates' => $items

                    ],
                    '@backend/views/mail/article-translation/article-translation'
                );
            }
        }
    }
}
