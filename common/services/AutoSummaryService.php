<?php

namespace common\services;

use common\models\Faq;
use common\models\Quiz;
use common\models\Shorts;
use common\models\Poll;
use common\models\PollTag;
use common\models\NewsContentSubdomain;
use common\models\Article;
use common\models\BoardContent;
use Yii;

class AutoSummaryService
{
    /**
     * Save summary data to separate database tables
     * @param array $summaryData
     * @param int $entityId
     * @param string $entity Entity type (news, article, boards)
     * @param string $langCode Language code
     * @return bool
     */
    public function saveSummaryToTables($summaryData, $entityId, $entity, $langCode = 1) //1 en, 2 hi
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            // Update existing data for this entity and language

            // Update or create header and paragraph summary
            if (!empty($summaryData['header']) || !empty($summaryData['paragraphSummary']) || $summaryData['action'] === 'header') {
                $header = isset($summaryData['action']) && $summaryData['action'] === 'header' ? $summaryData['header'] : $summaryData['header'];
                $this->saveShortSummary($header, $summaryData['paragraphSummary'] ?? '', $summaryData['status'] ?? 1, $entityId, $entity, $langCode);
            }

            // Update or create key content (auto_shorts)
            if (!empty($summaryData['keyContent']) || $summaryData['action'] === 'keyContent') {
                $this->saveShorts($summaryData['keyContent'], $summaryData['key_content_status'] ?? [], $entityId, $entity, $langCode);
            }

            // Update or create FAQs
            if (!empty($summaryData['faqs']) || $summaryData['action'] === 'faqs') {
                $this->saveFaqs($summaryData['faqs'], $entityId, $entity, $langCode);
            }

            // Update or create Quiz
            if (!empty($summaryData['quiz']) || $summaryData['action'] === 'quiz') {
                $this->saveQuiz($summaryData['quiz'], $entityId, $entity, $langCode);
            }

            // Update or create Poll
            if (!empty($summaryData['poll']) || $summaryData['action'] === 'poll' && is_array($summaryData['poll'])) {
                $poll = isset($summaryData['action']) && $summaryData['action'] === 'poll' ? $summaryData : $summaryData['poll'];
                $this->savePoll($poll, $entityId, $entity, $langCode);
            }

            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error('Failed to save summary to tables: ' . $e->getMessage());
            return false;
        }
    }

    public function saveShortSummary($headerData, $paragraphSummary, $status, $entityId, $entity, $langCode = 1)
    {
        // Save paragraph_summary to the respective content table's short_summary column
        $contentModel = null;

        switch ($entity) {
            case 'news':
                $contentModel = NewsContentSubdomain::findOne(['news_id' => $entityId]);
                if ($contentModel) {
                    $contentModel->short_summary = $paragraphSummary;
                    $contentModel->save();
                }
                break;
            case 'article':
                $contentModel = Article::findOne($entityId);
                if ($contentModel) {
                    $contentModel->description = $paragraphSummary; // Using description field as short_summary
                    $contentModel->save();
                }
                break;
            case 'boards':
                $contentModel = BoardContent::findOne(['board_id' => $entityId]);
                if ($contentModel) {
                    $contentModel->short_description = $paragraphSummary; // Using short_description field
                    $contentModel->save();
                }
                break;
        }
    }

    public function saveShorts($shortsData, $shortDataStatus, $entityId, $entity, $langCode = 1)
    {
        // Get existing key content for this entity and language
        $existingShorts = Shorts::find()
            ->where(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode])
            ->orderBy(['sort_order' => SORT_ASC])
            ->all();

        // Update existing records or create new ones
        foreach ($shortsData as $index => $content) {
            $status = isset($shortDataStatus[$index]) ? $shortDataStatus[$index] : Shorts::STATUS_ACTIVE;

            if ($index == '_csrf-backend' || $index == 'action') {
                continue;
            }

            if (!empty(trim($content))) {
                $shorts = isset($existingShorts[$index]) ? $existingShorts[$index] : new Shorts();
                if (!$shorts->entity_id) {
                    $shorts->entity_id = $entityId;
                    $shorts->entity = $entity;
                    $shorts->lang_code = $langCode;
                }
                $shorts->content = trim($content);
                $shorts->sort_order = $index;
                $shorts->status = $status; // Default to active
                $shorts->save();
            }
        }

        // Mark extra records as is_deleted if new content has fewer items
        if (count($existingShorts) > count($shortsData)) {
            for ($i = count($shortsData); $i < count($existingShorts); $i++) {
                if (isset($existingShorts[$i])) {
                    $existingShorts[$i]->status = Shorts::STATUS_DELETED; // Mark as deleted
                    $existingShorts[$i]->save();
                }
            }
        }
    }
    public function saveFaqs($faqsData, $entityId, $entity, $langCode = 1)
    {
        // Get existing FAQ for this entity
        $existingFaq = Faq::findOne(['entity_id' => $entityId, 'entity' => $entity]);

        if (!$existingFaq) {
            $existingFaq = new Faq();
            $existingFaq->entity_id = $entityId;
            $existingFaq->entity = $entity;
        }

        // Prepare QnAs array for the existing Faq model structure
        $qnas = [];
        foreach ($faqsData as $index => $faq) {
            if (!empty(trim($faq['question'] ?? '')) && !empty(trim($faq['answer'] ?? ''))) {
                $qnas[] = [
                    'question' => trim($faq['question']),
                    'answer' => trim($faq['answer'])
                ];
            }
        }

        $existingFaq->qnas = $qnas;
        $existingFaq->status = Faq::STATUS_ACTIVE;
        $existingFaq->save();
    }

    public function savePoll($pollData, $entityId, $entity, $langCode = 1)
    {
        $pollData = $pollData[0] ?? null; // Assuming only one poll per entity
        if (!empty($pollData) && !empty(trim($pollData['question'] ?? ''))) {
            $pollTag = PollTag::findOne(['entity_id' => $entityId, 'entity' => $entity]);
            if (!empty($pollTag)) {
                $poll = Poll::findOne($pollTag->poll_id);
            } else {
                $poll = new Poll();
            }

            $poll->question = trim($pollData['question']);
            $options = array_map('trim', $pollData['options'] ?? []);
            $options = array_filter($options);
            $poll->options = json_encode(array_values($options));
            $poll->lang_code = $langCode;
            $poll->status = $pollData['status'] ?? 1;

            if ($poll->save()) {
                if (empty($pollTag)) {
                    $pollTag = new PollTag();
                    $pollTag->created_at = date('Y-m-d H:i:s');
                    $pollTag->entity = $entity;
                    $pollTag->entity_id = $entityId;
                }

                $pollTag->poll_id = $poll->id;
                $pollTag->lang_code = $langCode;
                $pollTag->expiry_date = date('Y-m-d', strtotime('+15 days'));
                $pollTag->updated_at = date('Y-m-d H:i:s');
                $pollTag->save();
            }
        }
    }

    public function saveQuiz($quizData, $entityId, $entity, $langCode = 1)
    {
        // Get existing quiz for this entity and language
        $existingQuiz = Quiz::find()
            ->where(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode])
            ->orderBy(['sort_order' => SORT_ASC])
            ->all();

        // Update existing records or create new ones
        foreach ($quizData as $index => $quiz) {
            $status = $quiz['status'] ?? Quiz::STATUS_ACTIVE;
            if (!empty(trim($quiz['question'] ?? ''))) {
                $quizModel = isset($existingQuiz[$index]) ? $existingQuiz[$index] : new Quiz();
                if (!$quizModel->entity_id) {
                    $quizModel->entity_id = $entityId;
                    $quizModel->entity = $entity;
                    $quizModel->lang_code = $langCode;
                }
                $quizModel->question = trim($quiz['question']);
                $quizModel->options = $quiz['options'] ?? [];
                $quizModel->correct_answer = $quiz['answer'] ?? [];
                $quizModel->sort_order = $index;
                $quizModel->status = $status; // Default to active
                $quizModel->save();
            }
        }

        // Mark extra records as is_deleted if new content has fewer items
        if (count($existingQuiz) > count($quizData)) {
            for ($i = count($quizData); $i < count($existingQuiz); $i++) {
                if (isset($existingQuiz[$i])) {
                    $existingQuiz[$i]->status = Quiz::STATUS_DELETED; // Mark as deleted
                    $existingQuiz[$i]->save();
                }
            }
        }
    }


    /**
     * Clean content for better summary generation
     *
     * @param string $content
     * @return string
     */
    public function cleanContentForSummary($content)
    {
        // Remove HTML tags
        $cleanContent = strip_tags($content);

        // Remove extra whitespace and line breaks
        $cleanContent = preg_replace('/\s+/', ' ', $cleanContent);

        // Remove common unwanted patterns
        $cleanContent = preg_replace('/\r\n|\r|\n/', ' ', $cleanContent);
        $cleanContent = preg_replace('/&nbsp;/', ' ', $cleanContent);
        $cleanContent = preg_replace('/\s{2,}/', ' ', $cleanContent);

        // Remove table-like content patterns
        $cleanContent = preg_replace('/Particulars\s+Details/', '', $cleanContent);
        $cleanContent = preg_replace('/\d{1,2}:\d{2}\s*(AM|PM)\s*-\s*\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/\d{1,2}:\d{2}\s*(AM|PM)\s*-\s*\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/After\s+\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/Expected Time \d+/', '', $cleanContent);

        // Remove "Also Read:" sections and everything after
        $cleanContent = preg_replace('/Also Read:.*$/is', '', $cleanContent);

        // Remove credit/debit card mentions and incomplete sentences
        $cleanContent = preg_replace('/Credit\/debit card.*$/is', '', $cleanContent);
        $cleanContent = preg_replace('/Scanned image.*$/is', '', $cleanContent);

        // Remove document requirements sections
        $cleanContent = preg_replace('/Documents Required.*$/is', '', $cleanContent);
        $cleanContent = preg_replace('/Candidates must have.*$/is', '', $cleanContent);

        // Clean up and trim
        $cleanContent = trim($cleanContent);

        return $cleanContent;
    }

    /**
     * Generate a proper excerpt from cleaned content
     *
     * @param string $cleanContent
     * @param string $title
     * @return string
     */
    public function generateExcerpt($cleanContent, $title)
    {
        // If content is too short, return as is
        if (strlen($cleanContent) <= 200) {
            return $cleanContent;
        }

        // Try to find the first few complete sentences
        $sentences = preg_split('/[.!?]+/', $cleanContent);
        $excerpt = '';
        $wordCount = 0;
        $maxWords = 150; // Target around 150 words

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (empty($sentence)) {
                continue;
            }

            $words = str_word_count($sentence);

            // If adding this sentence would exceed our word limit, stop
            if ($wordCount + $words > $maxWords && !empty($excerpt)) {
                break;
            }

            $excerpt .= $sentence . '. ';
            $wordCount += $words;

            // If we have a good amount of content, stop
            if ($wordCount >= 100 && $wordCount <= $maxWords) {
                break;
            }
        }

        // If we couldn't build a good excerpt from sentences, fall back to word limit
        if (empty($excerpt) || $wordCount < 50) {
            $words = explode(' ', $cleanContent);
            $excerpt = implode(' ', array_slice($words, 0, $maxWords)) . '.';
        }

        // Final cleanup
        $excerpt = trim($excerpt);

        // Ensure it ends with proper punctuation
        if (!preg_match('/[.!?]$/', $excerpt)) {
            $excerpt .= '.';
        }

        return $excerpt;
    }

    public static $postAction = [
        'save-short-summary' => 'header',
        'save-shorts' => 'keyContent',
        'save-faqs' => 'faqs',
        'save-quiz' => 'quiz',
        'save-poll' => 'poll',
    ];
}
