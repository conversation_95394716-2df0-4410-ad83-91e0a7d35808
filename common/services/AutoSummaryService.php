<?php

namespace common\services;

use common\models\AutoFaqs;
use common\models\AutoQuiz;
use common\models\AutoShorts;
use common\models\AutoShortSummary;
use common\models\Poll;
use common\models\PollTag;
use Yii;

class AutoSummaryService
{
    /**
     * Save summary data to separate database tables
     * @param array $summaryData
     * @param int $entityId
     * @param string $entity Entity type (news, article, boards)
     * @param string $langCode Language code
     * @return bool
     */
    public function saveSummaryToTables($summaryData, $entityId, $entity, $langCode = 1) //1 en, 2 hi
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            // Update existing data for this entity and language

            // Update or create header and paragraph summary
            if (!empty($summaryData['header']) || !empty($summaryData['paragraphSummary']) || $summaryData['action'] === 'header') {
                $header = isset($summaryData['action']) && $summaryData['action'] === 'header' ? $summaryData['header'] : $summaryData['header'];
                $this->saveShortSummary($header, $summaryData['paragraphSummary'] ?? '', $summaryData['status'] ?? 1, $entityId, $entity, $langCode);
            }

            // Update or create key content (auto_shorts)
            if (!empty($summaryData['keyContent']) || $summaryData['action'] === 'keyContent') {
                $this->saveShorts($summaryData['keyContent'], $summaryData['key_content_status'] ?? [], $entityId, $entity, $langCode);
            }

            // Update or create FAQs
            if (!empty($summaryData['faqs']) || $summaryData['action'] === 'faqs') {
                $this->saveFaqs($summaryData['faqs'], $entityId, $entity, $langCode);
            }

            // Update or create Quiz
            if (!empty($summaryData['quiz']) || $summaryData['action'] === 'quiz') {
                $this->saveQuiz($summaryData['quiz'], $entityId, $entity, $langCode);
            }

            // Update or create Poll
            if (!empty($summaryData['poll']) || $summaryData['action'] === 'poll' && is_array($summaryData['poll'])) {
                $poll = isset($summaryData['action']) && $summaryData['action'] === 'poll' ? $summaryData : $summaryData['poll'];
                $this->savePoll($poll, $entityId, $entity, $langCode);
            }

            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error('Failed to save summary to tables: ' . $e->getMessage());
            return false;
        }
    }

    public function saveShortSummary($headerData, $paragraphSummary, $status, $entityId, $entity, $langCode = 1)
    {
        $autoShortSummary = AutoShortSummary::findOne(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode]);
        if (!$autoShortSummary) {
            $autoShortSummary = new AutoShortSummary();
            $autoShortSummary->entity_id = $entityId;
            $autoShortSummary->entity = $entity;
            $autoShortSummary->lang_code = $langCode;
        }
        $autoShortSummary->header = $headerData ?? '';
        $autoShortSummary->paragraph_summary = $paragraphSummary;
        $autoShortSummary->status = $status;
        $autoShortSummary->save();
    }

    public function saveShorts($shortsData, $shortDataStatus, $entityId, $entity, $langCode = 1)
    {
        // Get existing key content for this entity and language
        $existingShorts = AutoShorts::find()
            ->where(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode])
            ->orderBy(['sort_order' => SORT_ASC])
            ->all();

        // Update existing records or create new ones
        foreach ($shortsData as $index => $content) {
            $status = isset($shortDataStatus[$index]) ? $shortDataStatus[$index] : AutoShorts::STATUS_ACTIVE;

            if ($index == '_csrf-backend' || $index == 'action') {
                continue;
            }

            if (!empty(trim($content))) {
                $autoShorts = isset($existingShorts[$index]) ? $existingShorts[$index] : new AutoShorts();
                if (!$autoShorts->entity_id) {
                    $autoShorts->entity_id = $entityId;
                    $autoShorts->entity = $entity;
                    $autoShorts->lang_code = $langCode;
                }
                $autoShorts->content = trim($content);
                $autoShorts->sort_order = $index;
                $autoShorts->status = $status; // Default to active
                $autoShorts->save();
            }
        }

        // Mark extra records as is_deleted if new content has fewer items
        if (count($existingShorts) > count($shortsData)) {
            for ($i = count($shortsData); $i < count($existingShorts); $i++) {
                if (isset($existingShorts[$i])) {
                    $existingShorts[$i]->status = AutoShorts::STATUS_DELETED; // Mark as deleted
                    $existingShorts[$i]->save();
                }
            }
        }
    }
    public function saveFaqs($faqsData, $entityId, $entity, $langCode = 1)
    {
        // Get existing FAQs for this entity and language
        $existingFaqs = AutoFaqs::find()
            ->where(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode])
            ->orderBy(['sort_order' => SORT_ASC])
            ->all();

        // Update existing records or create new ones
        foreach ($faqsData as $index => $faq) {
            $status = $faq['status'] ?? AutoFaqs::STATUS_ACTIVE;
            if (!empty(trim($faq['question'] ?? '')) && !empty(trim($faq['answer'] ?? ''))) {
                $autoFaq = isset($existingFaqs[$index]) ? $existingFaqs[$index] : new AutoFaqs();
                if (!$autoFaq->entity_id) {
                    $autoFaq->entity_id = $entityId;
                    $autoFaq->entity = $entity;
                    $autoFaq->lang_code = $langCode;
                }
                $autoFaq->question = trim($faq['question']);
                $autoFaq->answer = trim($faq['answer']);
                $autoFaq->sort_order = $index;
                $autoFaq->status = $status; // Default to active
                $autoFaq->save();
            }
        }

        // Mark extra records as is_deleted if new content has fewer items
        if (count($existingFaqs) > count($faqsData)) {
            for ($i = count($faqsData); $i < count($existingFaqs); $i++) {
                if (isset($existingFaqs[$i])) {
                    $existingFaqs[$i]->status = AutoFaqs::STATUS_DELETED; // Mark as deleted
                    $existingFaqs[$i]->save();
                }
            }
        }
    }

    public function savePoll($pollData, $entityId, $entity, $langCode = 1)
    {
        $pollData = $pollData[0] ?? null; // Assuming only one poll per entity
        if (!empty($pollData) && !empty(trim($pollData['question'] ?? ''))) {
            $pollTag = PollTag::findOne(['entity_id' => $entityId, 'entity' => $entity]);
            if (!empty($pollTag)) {
                $poll = Poll::findOne($pollTag->poll_id);
            } else {
                $poll = new Poll();
            }

            $poll->question = trim($pollData['question']);
            $options = array_map('trim', $pollData['options'] ?? []);
            $options = array_filter($options);
            $poll->options = json_encode(array_values($options));
            $poll->lang_code = $langCode;
            $poll->status = $pollData['status'] ?? 1;

            if ($poll->save()) {
                if (empty($pollTag)) {
                    $pollTag = new PollTag();
                    $pollTag->created_at = date('Y-m-d H:i:s');
                    $pollTag->entity = $entity;
                    $pollTag->entity_id = $entityId;
                }

                $pollTag->poll_id = $poll->id;
                $pollTag->lang_code = $langCode;
                $pollTag->expiry_date = date('Y-m-d', strtotime('+15 days'));
                $pollTag->updated_at = date('Y-m-d H:i:s');
                $pollTag->save();
            }
        }
    }

    public function saveQuiz($quizData, $entityId, $entity, $langCode = 1)
    {
        // Get existing quiz for this entity and language
        $existingQuiz = AutoQuiz::find()
            ->where(['entity_id' => $entityId, 'entity' => $entity, 'lang_code' => $langCode])
            ->orderBy(['sort_order' => SORT_ASC])
            ->all();

        // Update existing records or create new ones
        foreach ($quizData as $index => $quiz) {
            $status = $quiz['status'] ?? AutoQuiz::STATUS_ACTIVE;
            if (!empty(trim($quiz['question'] ?? ''))) {
                $autoQuiz = isset($existingQuiz[$index]) ? $existingQuiz[$index] : new AutoQuiz();
                if (!$autoQuiz->entity_id) {
                    $autoQuiz->entity_id = $entityId;
                    $autoQuiz->entity = $entity;
                    $autoQuiz->lang_code = $langCode;
                }
                $autoQuiz->question = trim($quiz['question']);
                $autoQuiz->options = $quiz['options'] ?? [];
                $autoQuiz->correct_answer = $quiz['answer'] ?? [];
                $autoQuiz->sort_order = $index;
                $autoQuiz->status = $status; // Default to active
                $autoQuiz->save();
            }
        }

        // Mark extra records as is_deleted if new content has fewer items
        if (count($existingQuiz) > count($quizData)) {
            for ($i = count($quizData); $i < count($existingQuiz); $i++) {
                if (isset($existingQuiz[$i])) {
                    $existingQuiz[$i]->status = AutoQuiz::STATUS_DELETED; // Mark as deleted
                    $existingQuiz[$i]->save();
                }
            }
        }
    }


    /**
     * Clean content for better summary generation
     *
     * @param string $content
     * @return string
     */
    public function cleanContentForSummary($content)
    {
        // Remove HTML tags
        $cleanContent = strip_tags($content);

        // Remove extra whitespace and line breaks
        $cleanContent = preg_replace('/\s+/', ' ', $cleanContent);

        // Remove common unwanted patterns
        $cleanContent = preg_replace('/\r\n|\r|\n/', ' ', $cleanContent);
        $cleanContent = preg_replace('/&nbsp;/', ' ', $cleanContent);
        $cleanContent = preg_replace('/\s{2,}/', ' ', $cleanContent);

        // Remove table-like content patterns
        $cleanContent = preg_replace('/Particulars\s+Details/', '', $cleanContent);
        $cleanContent = preg_replace('/\d{1,2}:\d{2}\s*(AM|PM)\s*-\s*\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/\d{1,2}:\d{2}\s*(AM|PM)\s*-\s*\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/After\s+\d{1,2}:\d{2}\s*(AM|PM)/', '', $cleanContent);
        $cleanContent = preg_replace('/Expected Time \d+/', '', $cleanContent);

        // Remove "Also Read:" sections and everything after
        $cleanContent = preg_replace('/Also Read:.*$/is', '', $cleanContent);

        // Remove credit/debit card mentions and incomplete sentences
        $cleanContent = preg_replace('/Credit\/debit card.*$/is', '', $cleanContent);
        $cleanContent = preg_replace('/Scanned image.*$/is', '', $cleanContent);

        // Remove document requirements sections
        $cleanContent = preg_replace('/Documents Required.*$/is', '', $cleanContent);
        $cleanContent = preg_replace('/Candidates must have.*$/is', '', $cleanContent);

        // Clean up and trim
        $cleanContent = trim($cleanContent);

        return $cleanContent;
    }

    /**
     * Generate a proper excerpt from cleaned content
     *
     * @param string $cleanContent
     * @param string $title
     * @return string
     */
    public function generateExcerpt($cleanContent, $title)
    {
        // If content is too short, return as is
        if (strlen($cleanContent) <= 200) {
            return $cleanContent;
        }

        // Try to find the first few complete sentences
        $sentences = preg_split('/[.!?]+/', $cleanContent);
        $excerpt = '';
        $wordCount = 0;
        $maxWords = 150; // Target around 150 words

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (empty($sentence)) {
                continue;
            }

            $words = str_word_count($sentence);

            // If adding this sentence would exceed our word limit, stop
            if ($wordCount + $words > $maxWords && !empty($excerpt)) {
                break;
            }

            $excerpt .= $sentence . '. ';
            $wordCount += $words;

            // If we have a good amount of content, stop
            if ($wordCount >= 100 && $wordCount <= $maxWords) {
                break;
            }
        }

        // If we couldn't build a good excerpt from sentences, fall back to word limit
        if (empty($excerpt) || $wordCount < 50) {
            $words = explode(' ', $cleanContent);
            $excerpt = implode(' ', array_slice($words, 0, $maxWords)) . '.';
        }

        // Final cleanup
        $excerpt = trim($excerpt);

        // Ensure it ends with proper punctuation
        if (!preg_match('/[.!?]$/', $excerpt)) {
            $excerpt .= '.';
        }

        return $excerpt;
    }

    public static $postAction = [
        'save-short-summary' => 'header',
        'save-shorts' => 'keyContent',
        'save-faqs' => 'faqs',
        'save-quiz' => 'quiz',
        'save-poll' => 'poll',
    ];
}
