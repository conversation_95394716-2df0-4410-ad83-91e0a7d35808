<?php

namespace backend\controllers;

use backend\models\NewsSubdomainSearch;
use common\models\NewsContentSubdomain;
use Yii;
use Carbon\Carbon;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use yii\web\Response;
use common\services\S3Service;
use common\helpers\DataHelper;
use common\models\CollegeContent;
use common\models\News;
use common\models\NewsSubdomain;
use common\services\v2\NewsService;
use api\controllers\SummaryController;
use common\models\Poll;
use common\models\PollTag;
use common\services\AutoSummaryService;
use yii\db\Query;

/**
 * NewsController implements the CRUD actions for News model.
 */
class NewsSubdomainController extends Controller
{

    /**
     * Lists all News models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NewsSubdomainSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single News model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        if (!empty($model->audio)) {
            $model->audio =  DataHelper::s3Path($model->audio, 'news_audio', true);
        }
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Creates a new News model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new NewsSubdomain();
        $modelContent = new NewsContentSubdomain();
        $model->setScenario('create');
        $postRequest = Yii::$app->request->post();
        $currentTimestamp = Carbon::now()->toDateTimeString();
        $articleIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $newsIds = [];
        $courseIds = [];
        $tagsIds = [];
        $translationIds = [];

        if (!empty($postRequest['NewsSubdomain']['tags'])) {
            $tagsIds = $postRequest['NewsSubdomain']['tags'];
        }
        if (!empty($postRequest['NewsSubdomain']['news'])) {
            $newsIds = $postRequest['NewsSubdomain']['news'];
        }
        if (!empty($postRequest['NewsSubdomain']['article'])) {
            $articleIds = $postRequest['NewsSubdomain']['article'];
        }
        if (!empty($postRequest['NewsSubdomain']['college'])) {
            $collegeIds = $postRequest['NewsSubdomain']['college'];
        }
        if (!empty($postRequest['NewsSubdomain']['exam'])) {
            $examIds = $postRequest['NewsSubdomain']['exam'];
        }

        if (!empty($postRequest['NewsSubdomain']['board'])) {
            $boardIds = $postRequest['NewsSubdomain']['board'];
        }
        if (!empty($postRequest['NewsSubdomain']['course'])) {
            $courseIds = $postRequest['NewsSubdomain']['course'];
        }
        if (!empty($postRequest['NewsSubdomain']['translation'])) {
            $translationIds = $postRequest['NewsSubdomain']['translation'];
        }
        if (!empty($postRequest['NewsSubdomain']['bucket_id'])) {
            $model->bucket_id = $postRequest['NewsSubdomain']['bucket_id'];
        }
        if ($model->load($postRequest)) {
            $model->lang_code = empty($model->lang_code) ? NewsSubdomain::DEFAULT_LANG_CODE : $model->lang_code;
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 04-04-2023
             */
            if (isset($model->audio)) {
                $newsAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($newsAudio)) {
                    $newFileName = md5($currentTimestamp) . '.' . $newsAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'news_audio'), $newsAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            if (isset($model->banner_image)) {
                $bannerImage = UploadedFile::getInstance($model, 'banner_image');
                if (isset($bannerImage)) {
                    $newImageName = md5($currentTimestamp) . '.' . $bannerImage->getExtension();
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'news_genral'), $bannerImage->tempName);
                    if ($saveImage) {
                        $model->banner_image = $newImageName;
                    }
                }
            }
            if ($model->save()) {
                if (!empty($postRequest['NewsContentSubdomain'])) {
                    $contentData = $postRequest['NewsContentSubdomain'];
                    $modelContent = new NewsContentSubdomain();
                    $modelContent->author_id = $contentData['author_id'] ?? null;
                    $modelContent->h1 = $contentData['h1'] ?? null;
                    $modelContent->meta_title = $contentData['meta_title'] ?? null;
                    $modelContent->meta_description = $contentData['meta_description'] ?? null;
                    $modelContent->meta_keywords = $contentData['meta_keywords'] ?? null;
                    $modelContent->content = $contentData['content'] ?? null;
                    $modelContent->editor_remark = $contentData['editor_remark'] ?? null;
                    $modelContent->status = NewsContentSubdomain::STATUS_ACTIVE;
                    $modelContent->news_id = $model->id;
                    $modelContent->save();
                }
                $model->saveTags($tagsIds);
                $model->saveArticle($articleIds);
                $model->saveNews($newsIds);
                $model->saveCollege($collegeIds);
                $model->saveExam($examIds);
                $model->saveBoard($boardIds);
                $model->saveCourse($courseIds);
                $model->saveTranslation($translationIds);

                if (!empty($postRequest['NewsSubdomain']['college']) && $postRequest['NewsSubdomain']['status'] == 1) {
                    NewsService::createCollegeContentNews($postRequest['NewsSubdomain']['college']);
                }

                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                print_r($model->getErrors());
            }
        } else {
            return $this->render('create', [
                'model' => $model,
                'modelContent' => $modelContent
            ]);
        }
    }

    /**
     * Updates an existing News model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        if (!empty($model)) {
            $newsContentId = NewsContentSubdomain::find()->select(['id'])->where(['news_id' => $id])->one();
            if (!empty($newsContentId)) {
                $modelContent = $this->findModelContent($newsContentId);
            } else {
                $modelContent = new NewsContentSubdomain();
            }
        }
        $postRequest = Yii::$app->request->post();
        $currentTimestamp = Carbon::now()->toDateTimeString();
        $articleIds = [];
        $collegeIds = [];
        $examIds = [];
        $boardIds = [];
        $newsIds = [];
        $courseIds = [];
        $tagsIds = [];
        $translationIds = [];
        if (!empty($postRequest['NewsSubdomain']['tags'])) {
            $tagsIds = $postRequest['NewsSubdomain']['tags'];
        }

        if (!empty($postRequest['NewsSubdomain']['article'])) {
            $articleIds = $postRequest['NewsSubdomain']['article'];
        }
        if (!empty($postRequest['NewsSubdomain']['news'])) {
            $newsIds = $postRequest['NewsSubdomain']['news'];
        }
        if (!empty($postRequest['NewsSubdomain']['college'])) {
            $collegeIds = $postRequest['NewsSubdomain']['college'];
        }
        if (!empty($postRequest['NewsSubdomain']['exam'])) {
            $examIds = $postRequest['NewsSubdomain']['exam'];
        }

        if (!empty($postRequest['NewsSubdomain']['board'])) {
            $boardIds = $postRequest['NewsSubdomain']['board'];
        }
        if (!empty($postRequest['NewsSubdomain']['course'])) {
            $courseIds = $postRequest['NewsSubdomain']['course'];
        }
        if (!empty($postRequest['NewsSubdomain']['translation'])) {
            $translationIds = $postRequest['NewsSubdomain']['translation'];
        }
        if (!empty($postRequest['NewsSubdomain']['bucket_id'])) {
            $model->bucket_id = $postRequest['NewsSubdomain']['bucket_id'];
        }
        if (!empty($postRequest['NewsSubdomain']['slug'])) {
            $checkUnique = NewsSubdomain::find()->where(['slug' => $postRequest['NewsSubdomain']['slug']])
                ->andWhere(['!=', 'id', $id])
                ->count();

            if ($checkUnique > 0) {
                $model->addError('slug', 'Slug must be unique');
                return $this->render('update', [
                    'model' => $model
                ]);
            }
        }

        $oldNewsImage = $model->banner_image;
        $oldNewsAudio = $model->audio;



        if ($model->load($postRequest)) {
            /**
             * Below code is used for audio file upload in the s3
             * <AUTHOR> Date: 04-04-2023
             */

            if (isset($model->audio)) {
                $newsAudio = \yii\web\UploadedFile::getInstance($model, 'audio');
                if (isset($newsAudio)) {
                    $newFileName = md5($currentTimestamp) . '.' . $newsAudio->getExtension();
                    $model->audio = $newFileName ?? '';
                    $s3 = new S3Service();
                    $saveAudio = $s3->uploadFile(DataHelper::s3Path($newFileName, 'news_audio'), $newsAudio->tempName);
                    if ($saveAudio) {
                        $model->audio = $newFileName ?? '';
                    }
                }
            }
            //end of audio file capture
            $bannerImage = UploadedFile::getInstance($model, 'banner_image');
            if (!empty($bannerImage)) {
                $newImageName = md5($currentTimestamp) . '.' . $bannerImage->getExtension();
                $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($newImageName, 'news_genral'), $bannerImage->tempName);
                if ($saveImage) {
                    $model->banner_image = $newImageName;
                    // $model->saveTags($tagsIds);
                    // $model->saveArticle($articleIds);
                    // $model->saveNews($newsIds);
                    // $model->saveCollege($collegeIds);
                    // $model->saveExam($examIds);
                    // $model->saveBoard($boardIds);
                    // $model->saveCourse($courseIds);
                    // $model->saveTranslation($translationIds);
                    // $model->save();

                    // return $this->redirect(['view', 'id' => $model->id]);
                }
            } else {
                $model->banner_image = $oldNewsImage ?? '';
                if (empty($model->audio)) {
                    $model->audio = $oldNewsAudio ?? '';
                }
            }
            $model->saveTags($tagsIds);
            $model->saveArticle($articleIds);
            $model->saveNews($newsIds);
            $model->saveCollege($collegeIds);
            $model->saveExam($examIds);
            $model->saveBoard($boardIds);
            $model->saveCourse($courseIds);
            $model->saveTranslation($translationIds);


            if ($model->save()) {
                $modelContent->author_id = $postRequest['NewsContentSubdomain']['author_id'];
                $modelContent->h1 = $postRequest['NewsContentSubdomain']['h1'];
                $modelContent->meta_title = $postRequest['NewsContentSubdomain']['meta_title'];
                $modelContent->meta_description = $postRequest['NewsContentSubdomain']['meta_description'];
                $modelContent->meta_keywords = $postRequest['NewsContentSubdomain']['meta_keywords'];
                $modelContent->content = $postRequest['NewsContentSubdomain']['content'];
                $modelContent->editor_remark = $postRequest['NewsContentSubdomain']['editor_remark'];
                $modelContent->status = NewsContentSubdomain::STATUS_ACTIVE;
                $modelContent->news_id = $model->id;
                $modelContent->save();
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                // Handle validation errors
                $errors = $model->errors;
                if (!empty($errors)) {
                    // Convert errors to a single message
                    $errorMessage = '';
                    foreach ($errors as $attributeErrors) {
                        $errorMessage .= implode(' ', $attributeErrors) . ' ';
                    }
                    Yii::$app->session->setFlash('error', trim($errorMessage));
                } else {
                    Yii::$app->session->setFlash('error', 'Failed to save record due to unknown error.');
                }
                return $this->redirect(['update', 'id' => $model->id]);
            }
        } else {
            if (!empty($model->audio)) {
                $model->audio =  DataHelper::s3Path($model->audio, 'news_audio', true);
            }
            return $this->render('update', [
                'model' => $model,
                'modelContent' => $modelContent
            ]);
        }
    }

    /**
     * Deletes an existing News model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    /*public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }*/

    /**
     * Finds the News model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return News the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = NewsSubdomain::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function findModelContent($id)
    {
        if (($model = NewsContentSubdomain::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }



    /**
     * Displays a single NewsSubdomainContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        $modelContent = NewsContentSubdomain::find()->where(['news_id' => $id])->one();
        if (!empty($modelContent)) {
            return $this->render('preview', [
                'model' => $modelContent,
            ]);
        } else {
            throw new NotFoundHttpException('News Content not found');
        }
    }

    /**
     * Generate or edit auto summary for news content
     * @param integer $id
     * @return mixed
     */
    public function actionAutoSummary($id)
    {
        $model = $this->findModel($id);
        $modelContent = NewsContentSubdomain::find()->where(['news_id' => $id])->one();

        if (!$modelContent) {
            throw new NotFoundHttpException('News Content not found');
        }

        $entity = 'news';
        $entityId = $id;

        $pollTag = PollTag::find()->select(['poll_id'])->where(['entity' => $entity, 'entity_id' => $entityId])->one();

        if (!empty($pollTag) && !empty($pollTag->poll_id)) {
            $pollData = Poll::find()->where(['id' => $pollTag->poll_id])->asArray()->one();
            if (!empty($pollData['options'])) {
                $options = $pollData['options'];
                if (is_string($options)) {
                    $pollData['options'] = json_decode(json_decode($options, true));
                }
            }
        }

        if (Yii::$app->request->isPost) {
            $postData = Yii::$app->request->post();

            if (!empty($postData['action'])) {
                if ($postData['action'] === 'generate') {
                    // Generate new summary
                    try {
                        $summaryData = SummaryController::generateSummary($modelContent->h1, $modelContent->content, $model->id, 'news', $model->lang_code);

                        if ($summaryData && is_array($summaryData)) {
                            Yii::$app->session->setFlash('success', 'Auto summary generated successfully!');
                        } else {
                            Yii::$app->session->setFlash('error', 'Failed to generate summary from API.');
                        }
                    } catch (\Exception $e) {
                        Yii::$app->session->setFlash('error', 'Error generating summary: ' . $e->getMessage());
                    }
                } else {
                    $postData['action'] = AutoSummaryService::$postAction[$postData['action']] ?? $postData['action'];
                    $result = (new AutoSummaryService())->saveSummaryToTables($postData, $model->id, 'news', $model->lang_code);

                    if ($result) {
                        Yii::$app->session->setFlash('success', $postData['action'] . ' saved successfully!');
                        if (Yii::$app->request->isAjax) {
                            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                            return ['success' => true, 'message' => $postData['action'] . ' saved successfully!'];
                        } else {
                            return $this->redirect(['auto-summary', 'id' => $model->id]);
                        }
                    } else {
                        Yii::$app->session->setFlash('error', 'Failed to save ' . $postData['action']);
                        if (Yii::$app->request->isAjax) {
                            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
                            return ['success' => false, 'message' => 'Failed to save ' . $postData['action']];
                        } else {
                            return $this->redirect(['auto-summary', 'id' => $model->id]);
                        }
                    }
                }
            }
            return $this->redirect(['auto-summary', 'id' => $id, 'entity' => $entity]);
        }

        return $this->render('auto-summary', [
            'model' => $model,
            'modelContent' => $modelContent,
            'pollData' => $pollData ?? [],
            'entity' => $entity,
        ]);
    }
}
