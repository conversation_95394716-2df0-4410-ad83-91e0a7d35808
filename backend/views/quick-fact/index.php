<?php

use common\helpers\DataHelper;
use common\models\QuickFact;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\QuickFactSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Quick Facts';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="quick-fact-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Poll Tag', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'id',
            [
                'attribute' => 'entity',
                'filter' => Html::activeDropDownList(
                    $searchModel,
                    'entity',
                    ArrayHelper::map(QuickFact::find()->select('entity')->distinct()->all(), 'entity', 'entity'),
                    ['class' => 'form-control', 'prompt' => '']
                ),
            ],
            [
                'attribute' => 'entity_id',
                'label' => 'Title',
                'value' => function ($model) {
                    return $model->getTitle();
                }
            ],
            'title',
            [
                'attribute' => 'lang_code',
                'value' => function ($model) {
                    if (!empty($model['lang_code'])) {
                        return array_search($model['lang_code'], DataHelper::$languageCode);
                    }
                },
                'filter' => array_flip(DataHelper::$languageCode)
            ],
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', QuickFact::class), $model->status);
                },
                'filter' => DataHelper::getConstantList('STATUS', QuickFact::class)
            ],
            
            ['class' => 'yii\grid\ActionColumn', 'template' => '{view} {update}'],
        ],
    ]); ?>


</div>