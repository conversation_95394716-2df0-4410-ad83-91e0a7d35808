<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\QuickFact;
use kartik\datetime\DateTimePicker;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\QuickFact */
/* @var $form yii\widgets\ActiveForm */

$entitiesArr = DataHelper::$entities;
$excludedArr = ['NCERT', 'FILTER', 'CAREER', 'COURSE', 'EXAM', 'BOARD', 'COLLEGE', 'NEWS'];
foreach ($excludedArr as $value) {
    if (($key = array_search($value, $entitiesArr)) !== false) {
        unset($entitiesArr[$key]);
    }
}
?>

<div class="quick-fact-form box box-primary">

    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">
        <div class="text-muted well well-sm no-shadow">

            <div class="row">
                <div class="col-md-4">
                    <?= $form->field($model, 'entity')->dropDownList($entitiesArr, [
                        'id' => 'entity',
                        'options' => [
                            'others' => ['Selected' => true],
                        ],
                        'disabled' => !$model->isNewRecord,
                    ])->label('Type*') ?>
                </div>
                <div class="col-md-4">
                    <?= $form->field($model, 'lang_code')->dropDownList(
                        array_flip(DataHelper::$languageCode),
                        [
                            'id' => 'lang_code',
                        ]
                    )->label('Language code') ?>
                </div>
                <div class="col-md-4">
                    <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', QuickFact::class)); ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
                        'data' => !empty($model->getTitle()) ? [$model->entity_id => $model->getTitle()] : [],
                        'language' => 'en',
                        'options' => [
                            'placeholder' => '--Select--',
                            'multiple' => false,
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'placeholder' => '--Select--',
                            'disabled' => !$model->isNewRecord,
                            'minimumInputLength' => 3,
                            'language' => [
                                'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                            ],
                            'ajax' => [
                                'url' => Url::to(['/poll-tag/get-list']),
                                'dataType' => 'json',
                                'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity').val(),lang_id:$('#lang_code').val()}; }")
                            ],
                            'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                            'templateResult' => new JsExpression('function(data) { return data.text; }'),
                            'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                        ],
                    ])->label('Select Articles/News*'); ?>
                </div>
                <div class="col-md-4">
                    <?= $form->field($model, 'published_at')->widget(DateTimePicker::class, [
                        'options' => ['placeholder' => 'Select publish date & time'],
                        'pluginOptions' => [
                            'autoclose' => true,
                            'format' => 'yyyy-mm-dd hh:ii:ss',
                            'todayHighlight' => true,
                            'todayBtn' => true,
                            'startDate' => date('Y-m-d H:i'), // disables past date/time
                        ]
                    ]); ?>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <?= $form->field($model, 'title')->textInput(['maxlength' => true]) ?>
                </div>
                <div class="col-md-6">
                    <?= $form->field($model, 'h1')->textInput(['maxlength' => true]) ?>
                </div>
            </div>

            <?=
            $this->render('/widget/tinymce', [
                'form' => $form,
                'model' => $model,
                'entity' => 'content',
                'type' => Article::ENTITY_ARTICLE
            ])
            ?>

            <div class="form-group">
                <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
            </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>