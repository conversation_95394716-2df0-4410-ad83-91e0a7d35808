<?php

use common\helpers\DataHelper;
use common\models\QuickFact;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\QuickFact */

$this->title = 'Quick Fact';
$this->params['breadcrumbs'][] = ['label' => 'Quick Facts', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

?>
<div class="quick-fact-view box box-primary">

    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>

    <?= DetailView::widget([
        'model' => $model,
        'attributes' => [
            'id',
            [
                'label' => 'Entity',
                'attribute' => 'entity',
            ],
            [
                'label' => 'Language Code',
                'attribute' => 'lang_code',
                'format' => 'html',
                'value' => function ($model) {
                    if (!empty($model->lang_code)) {
                        return array_search($model->lang_code, DataHelper::$languageCode);
                    }
                }
            ],
            [
                'attribute' => 'entity_id',
                'label' => 'Title',
                'value' => function ($model) {
                    return $model->getTitle();
                }
            ],
            'h1',
            [
                'label' => 'Document details',
                'format' => 'raw',
                'value' => html_entity_decode($model->content),
            ],
            [
                'label' => 'Source Type',
                'attribute' => 'source_type',
                'value' => ArrayHelper::getValue(DataHelper::getConstantList('SOURCE_TYPE', QuickFact::class), $model->source_type)
            ],
            [
                'label' => 'Status',
                'attribute' => 'status',
                'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', QuickFact::class), $model->status)
            ],
            'published_at:datetime',
            'created_at:datetime',
            'updated_at:datetime',
        ],
    ]) ?>

</div>