<?php

use common\helpers\DataHelper;
use common\models\Article;
use common\models\Poll;
use common\models\PollTag;
use Mpdf\Tag\Em;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\NewsSubdomain */
/* @var $modelContent common\models\NewsContentSubdomain */

$this->title = 'Auto Summary: ' . $model->name;
$this->params['breadcrumbs'][] = ['label' => 'News Subdomain', 'url' => ['index']];
$this->params['breadcrumbs'][] = ['label' => $model->name, 'url' => ['view', 'id' => $model->id]];
$this->params['breadcrumbs'][] = 'Auto Summary';

// Register CSS
$this->registerCssFile('@web/css/auto-summary.css');

// Load data from database tables
$newsContent = $model->newsContent; // For short_summary
$shorts = $model->shorts;
$faqs = $model->faqs;
$quiz = $model->quiz;

// Check if any summary data exists
$hasSummaryData = ($newsContent && $newsContent->short_summary) || !empty($shorts) || !empty($faqs) || !empty($quiz);

$quickFacts = [];
$pollData = [];
$entityArr = ['news', 'article'];
$entity = isset($_GET['entity']) ? $_GET['entity'] : null;
$entityId = isset($_GET['id']) ? $_GET['id'] : null;

if (empty($quickFacts)) {
    $quickFacts = ['Quick fact 1', 'Quick fact 2', 'Quick fact 3', 'Quick fact 4', 'Quick fact 5'];
}

$pollTag = PollTag::find()->select(['poll_id'])->where(['entity' => $entity, 'entity_id' => $entityId])->one();
if (!empty($pollTag) && !empty($pollTag->poll_id)) {
    $pollData = Poll::find()->where(['id' => $pollTag->poll_id])->asArray()->one();
    if (!empty($pollData['options']) && is_string($pollData['options'])) {
        $pollData['options'] = json_decode(json_decode($pollData['options'], true), true);
    }
} else {
    $pollData = ['question' => 'Test Question', 'options' => ['Test option 1', 'Test option 2', 'Test option 3', 'Test option 4'], 'status' => 1];
}

?>

<style>
    .content-header {
        display: none;
    }

    .generate-summary-button,
    .save-changes-button {
        margin-right: 10px;
    }

    .summary-editor-container {
        margin-bottom: 20px;
    }

    .summary-section {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f9f9f9;
    }

    .summary-section h5 {
        margin-top: 0;
        color: #333;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
    }

    .quiz-options {
        list-style-type: none;
        padding-left: 0;
    }

    .quiz-options li {
        padding: 5px 10px;
        margin: 5px 0;
        background-color: #f0f0f0;
        border-radius: 3px;
    }

    .quiz-options li.correct-answer {
        background-color: #d4edda;
        color: #155724;
        font-weight: bold;
    }

    .poll-item,
    .quiz-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        background-color: #fafafa;
    }
</style>

<div class="news-auto-summary">
    <!-- Header Section -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <i class="fa fa-magic"></i> <?= Html::encode($this->title) ?>
                    </h3>
                    <div class="box-tools pull-right">
                        <?= Html::a('<i class="fa fa-arrow-left"></i> Back to News', ['view', 'id' => $model->id], [
                            'class' => 'btn btn-default btn-sm'
                        ]) ?>
                    </div>
                </div>

                <?php if (!$hasSummaryData): ?>
                    <div class="box-body">
                        <!-- Generate Button for Empty Summary -->
                        <div class="text-center" style="margin-bottom: 20px;">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> No auto summary has been generated yet. Click the button below to generate one.
                            </div>
                            <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['style' => 'display: inline-block;']) ?>
                            <?= Html::hiddenInput('action', 'generate') ?>
                            <?= Html::submitButton(
                                '<i class="fa fa-magic"></i> Generate Auto Summary',
                                [
                                    'class' => 'btn btn-primary btn-lg',
                                    'onclick' => 'return confirm("This will generate a new summary. Continue?")'
                                ]
                            ) ?>
                            <?= Html::endForm() ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="box-body">
                        <!-- Action Buttons for Existing Summary -->
                        <div class="form-group" style="margin-top: 15px;">
                            <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['style' => 'display: inline-block;']) ?>
                            <?= Html::hiddenInput('action', 'generate') ?>
                            <?= Html::submitButton(
                                '<i class="fa fa-magic"></i> Regenerate Summary',
                                [
                                    'class' => 'btn btn-warning',
                                    'onclick' => 'return confirm("This will replace the existing summary. Continue?")'
                                ]
                            ) ?>
                            <?= Html::endForm() ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Header and Paragraph Summary Section -->
    <?php if ($hasSummaryData): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default collapsed-box">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" href="#shorts-section" aria-expanded="false">
                                <i class="fa fa-info-circle"></i> Header & Summary
                            </a>
                        </h4>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body collapse" id="shorts-section">
                        <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['id' => 'shorts-form']) ?>
                        <?= Html::hiddenInput('action', 'save-short-summary') ?>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label class="control-label"><i class="fa fa-header"></i> Header</label>
                                    <?= Html::textInput('header', '', [
                                        'class' => 'form-control',
                                        'placeholder' => 'Enter the main header/title for the summary (not saved to database)'
                                    ]) ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label"><i class="fa fa-toggle-on"></i> Status</label>
                                    <?= Html::dropDownList('status', 1, [
                                        1 => 'Active',
                                        0 => 'Inactive'
                                    ], [
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="control-label"><i class="fa fa-paragraph"></i> Paragraph Summary</label>
                                    <?= Html::textarea('paragraphSummary', $newsContent->short_summary ?? '', [
                                        'class' => 'form-control',
                                        'rows' => 4,
                                        'placeholder' => 'Enter a brief paragraph summary of the content'
                                    ]) ?>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <?= Html::submitButton('<i class="fa fa-save"></i> Save Header & Summary', [
                                'class' => 'btn btn-success'
                            ]) ?>
                        </div>

                        <?= Html::endForm() ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- News Shorts -->
    <?php if (!empty($autoShorts)): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default collapsed-box">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" href="#key-content-section" aria-expanded="false">
                                <i class="fa fa-key"></i> News Shorts
                            </a>
                        </h4>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body collapse" id="key-content-section">
                        <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['id' => 'key-content-form']) ?>
                        <?= Html::hiddenInput('action', 'save-shorts') ?>
                        <?php foreach ($shorts as $index => $content): ?>
                            <div class="row" style="margin-bottom: 10px;">
                                <div class="col-md-9">
                                    <?= Html::textInput("keyContent[{$index}]", $content->content, [
                                        'class' => 'form-control',
                                        'placeholder' => 'Enter key content point'
                                    ]) ?>
                                </div>
                                <div class="col-md-3">
                                    <?= Html::dropDownList("key_content_status[{$index}]", $content->status ?? 1, [
                                        1 => 'Active',
                                        0 => 'Inactive'
                                    ], [
                                        'class' => 'form-control'
                                    ]) ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <?= Html::submitButton('<i class="fa fa-save"></i> Save Shorts Content', [
                                'class' => 'btn btn-success'
                            ]) ?>
                        </div>
                        <?= Html::endForm() ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- FAQs Section -->
    <?php if (!empty($faqs)): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default collapsed-box">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" href="#faqs-section" aria-expanded="false">
                                <i class="fa fa-question-circle"></i> FAQs
                            </a>
                        </h4>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body collapse" id="faqs-section">
                        <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['id' => 'faqs-form']) ?>
                        <?= Html::hiddenInput('action', 'save-faqs') ?>

                        <?php
                        $faqData = [];
                        if (!empty($faqs)) {
                            foreach ($faqs as $faq) {
                                if ($faq->qnas && is_array($faq->qnas)) {
                                    $faqData = $faq->qnas;
                                    break;
                                }
                            }
                        }
                        ?>
                        <?php foreach ($faqData as $index => $faqItem): ?>
                            <div class="panel panel-default" style="margin-bottom: 15px;">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <div class="form-group">
                                                <label>Question:</label>
                                                <?= Html::textInput("faqs[{$index}][question]", $faqItem['question'] ?? '', [
                                                    'class' => 'form-control',
                                                    'placeholder' => 'Enter FAQ question'
                                                ]) ?>
                                            </div>
                                            <div class="form-group">
                                                <label>Answer:</label>
                                                <?= Html::textarea("faqs[{$index}][answer]", $faqItem['answer'] ?? '', [
                                                    'class' => 'form-control',
                                                    'rows' => 3,
                                                    'placeholder' => 'Enter FAQ answer'
                                                ]) ?>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Status:</label>
                                                <?= Html::dropDownList("faqs[{$index}][status]", 1, [
                                                    1 => 'Active',
                                                    0 => 'Inactive'
                                                ], [
                                                    'class' => 'form-control'
                                                ]) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <?= Html::submitButton('<i class="fa fa-save"></i> Save FAQs', [
                                'class' => 'btn btn-success'
                            ]) ?>
                        </div>

                        <?= Html::endForm() ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quiz Section -->
    <?php if (!empty($quiz)): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default collapsed-box">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" href="#quiz-section" aria-expanded="false">
                                <i class="fa fa-graduation-cap"></i> Quiz
                            </a>
                        </h4>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body collapse" id="quiz-section">
                        <?= Html::beginForm(['auto-summary', 'id' => $model->id], 'post', ['id' => 'quiz-form']) ?>
                        <?= Html::hiddenInput('action', 'save-quiz') ?>
                        <?php foreach ($quiz as $index => $quizItem): ?>
                            <div class="panel panel-default" style="margin-bottom: 15px;">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <div class="form-group">
                                                <label>Question:</label>
                                                <?= Html::textInput("quiz[{$index}][question]", $quizItem->question, [
                                                    'class' => 'form-control',
                                                    'placeholder' => 'Enter quiz question'
                                                ]) ?>
                                            </div>
                                            <div class="form-group">
                                                <label>Options:</label>
                                                <?php if (!empty($quizItem->options)): ?>
                                                    <?php foreach ($quizItem->options as $optIndex => $option): ?>
                                                        <div class="form-group">
                                                            <?= Html::textInput("quiz[{$index}][options][{$optIndex}]", $option, [
                                                                'class' => 'form-control',
                                                                'placeholder' => 'Enter option ' . ($optIndex + 1)
                                                            ]) ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="form-group">
                                                <label>Correct Answer:</label>
                                                <?= Html::textInput("quiz[{$index}][answer]", $quizItem->correct_answer, [
                                                    'class' => 'form-control',
                                                    'placeholder' => 'Enter the correct answer'
                                                ]) ?>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Status:</label>
                                                <?= Html::dropDownList("quiz[{$index}][status]", $quizItem->status ?? 1, [
                                                    1 => 'Active',
                                                    0 => 'Inactive'
                                                ], [
                                                    'class' => 'form-control'
                                                ]) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center">
                            <?= Html::submitButton('<i class="fa fa-save"></i> Save Quiz', [
                                'class' => 'btn btn-success'
                            ]) ?>
                        </div>

                        <?= Html::endForm() ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Facts Section (Separate - No Table) -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-default collapsed-box">
                <div class="box-header with-border">
                    <h4 class="box-title">
                        <a data-toggle="collapse" href="#quick-facts-section" aria-expanded="false">
                            <i class="fa fa-check-circle"></i> Quick Facts (Display Only)
                        </a>
                    </h4>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse">
                            <i class="fa fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="box-body collapse" id="quick-facts-section">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> Quick Facts are for display purposes only and are not saved to database tables.
                    </div>
                    <?php foreach ($quickFacts as $index => $fact): ?>
                        <div class="form-group">
                            <?= Html::textInput("quick_facts[{$index}]", $fact, [
                                'class' => 'form-control',
                                'placeholder' => 'Enter a quick fact',
                                'readonly' => true
                            ]) ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Poll Section (Separate - No Table) -->
    <?php if (!empty($entity) && !empty($pollData) && in_array($entity, $entityArr)): ?>
        <div class="row">
            <div class="col-md-12">
                <div class="box box-default collapsed-box">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" href="#poll-section" aria-expanded="false">
                                <i class="fa fa-bar-chart"></i> Poll Questions
                            </a>
                        </h4>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body collapse" id="poll-section">
                        <?= Html::beginForm(['auto-summary', 'id' => $model->id, 'entity' => $entity], 'post', ['id' => 'polls-form']) ?>
                        <?= Html::hiddenInput('action', 'save-poll') ?>

                        <div class="panel panel-default" style="margin-bottom: 15px;">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Poll Question</label>
                                            <?= Html::textarea('poll[question]', $pollData['question'], [
                                                'class' => 'form-control',
                                                'rows' => 3,
                                                'entity' => 'question',
                                                'placeholder' => 'Enter Poll Question'
                                            ]) ?>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label>Poll Options:</label>
                                        <div class="row">
                                            <?php if (!empty($pollData['options']) && is_array($pollData['options'])):
                                                foreach ($pollData['options'] as $optIndex => $option): ?>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <?= Html::textInput("poll[options][{$optIndex}]", $option, [
                                                                'class' => 'form-control',
                                                                'placeholder' => 'Enter Option ' . ($optIndex + 1),
                                                            ]) ?>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Status:</label>
                                            <?= Html::dropDownList('poll[status]', $pollData['status'] ?? 1, [
                                                1 => 'Active',
                                                0 => 'Inactive'
                                            ], [
                                                'class' => 'form-control'
                                            ]) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center">
                            <?= Html::submitButton('<i class="fa fa-save"></i> Save Poll', [
                                'class' => 'btn btn-success'
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>