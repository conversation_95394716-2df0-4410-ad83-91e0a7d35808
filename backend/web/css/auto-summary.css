/* Auto Summary Feature Styles */

.auto-summary-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 10px 0;
}

.auto-summary-preview .label {
    margin-right: 10px;
}

.summary-editor-container {
    position: relative;
}

.summary-editor-toolbar {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom: none;
    padding: 10px;
    border-radius: 5px 5px 0 0;
}

.summary-editor-toolbar .btn {
    margin-right: 5px;
}

#summary-editor {
    border-radius: 0 0 5px 5px;
    border-top: none;
    resize: vertical;
    min-height: 400px;
}

.summary-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.summary-section h5 {
    margin-top: 0;
    color: #337ab7;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    font-weight: bold;
}

.quick-facts, .key-content {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.quick-facts li, .key-content li {
    padding: 8px 0;
    border-bottom: 1px dotted #ccc;
    position: relative;
    padding-left: 20px;
}

.quick-facts li:last-child, .key-content li:last-child {
    border-bottom: none;
}

.quick-facts li:before {
    content: "✓";
    color: #5cb85c;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 8px;
}

.key-content li:before {
    content: "•";
    color: #337ab7;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 8px;
}

.faq-item, .quiz-item {
    margin-bottom: 15px;
    padding: 15px;
    background-color: #fff;
    border-left: 4px solid #337ab7;
    border-radius: 0 5px 5px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.faq-item strong, .quiz-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 8px;
}

.quiz-options {
    list-style-type: none;
    padding-left: 20px;
    margin: 10px 0 0 0;
}

.quiz-options li {
    padding: 5px 0;
    position: relative;
}

.quiz-options li:before {
    content: "○";
    position: absolute;
    left: -15px;
    color: #666;
}

.correct-answer {
    color: #5cb85c !important;
    font-weight: bold;
}

.correct-answer:before {
    content: "●" !important;
    color: #5cb85c !important;
}

.poll-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
}

.poll-section h5 {
    color: white;
    border-bottom-color: rgba(255,255,255,0.3);
}

.poll-section .quiz-options li {
    color: rgba(255,255,255,0.9);
}

.poll-section .quiz-options li:before {
    color: rgba(255,255,255,0.7);
}

/* JSON Editor Enhancements */
.json-editor-container {
    position: relative;
}

.json-line-numbers {
    position: absolute;
    left: 0;
    top: 0;
    width: 40px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    padding: 10px 5px;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #6c757d;
    user-select: none;
    pointer-events: none;
}

.json-editor-with-numbers {
    padding-left: 50px;
}

/* Status indicators */
.summary-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.summary-status.generated {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.summary-status.not-generated {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.summary-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Button enhancements */
.btn-generate-summary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-generate-summary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #337ab7;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .summary-section {
        padding: 10px;
    }
    
    .faq-item, .quiz-item {
        padding: 10px;
    }
    
    #summary-editor {
        min-height: 300px;
    }
    
    .summary-editor-toolbar {
        text-align: center;
    }
    
    .summary-editor-toolbar .btn {
        margin: 2px;
        font-size: 12px;
    }
}

/* Print styles */
@media print {
    .summary-editor-toolbar,
    .btn,
    .box-tools {
        display: none !important;
    }
    
    .summary-section {
        break-inside: avoid;
        border: 1px solid #000;
        margin-bottom: 10px;
    }
}
