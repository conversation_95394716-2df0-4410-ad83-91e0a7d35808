<?php

use yii\db\Migration;

/**
 * Rename summary tables:
 * - news_shorts -> news_short_summary
 * - news_key_content -> news_shorts
 */
class m250107_130000_rename_summary_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Drop foreign keys first
        $this->dropForeignKey('fk-news_shorts-entity_id', '{{%news_shorts}}');
        $this->dropForeignKey('fk-news_key_content-entity_id', '{{%news_key_content}}');
        
        // Rename tables
        $this->renameTable('{{%news_shorts}}', '{{%news_short_summary}}');
        $this->renameTable('{{%news_key_content}}', '{{%news_shorts}}');
        
        // Recreate foreign keys with new table names
        $this->addForeignKey(
            'fk-news_short_summary-entity_id',
            '{{%news_short_summary}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        
        $this->addForeignKey(
            'fk-news_shorts-entity_id',
            '{{%news_shorts}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop foreign keys first
        $this->dropForeignKey('fk-news_short_summary-entity_id', '{{%news_short_summary}}');
        $this->dropForeignKey('fk-news_shorts-entity_id', '{{%news_shorts}}');
        
        // Rename tables back
        $this->renameTable('{{%news_short_summary}}', '{{%news_shorts}}');
        $this->renameTable('{{%news_shorts}}', '{{%news_key_content}}');
        
        // Recreate original foreign keys
        $this->addForeignKey(
            'fk-news_shorts-entity_id',
            '{{%news_shorts}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
        
        $this->addForeignKey(
            'fk-news_key_content-entity_id',
            '{{%news_key_content}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }
}
