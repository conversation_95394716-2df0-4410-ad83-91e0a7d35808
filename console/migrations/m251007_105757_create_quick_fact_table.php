<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%quick_fact}}`.
 */
class m251007_105757_create_quick_fact_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $options = null;
        if (\Yii::$app->db->getDriverName() === 'mysql') {
            $options = 'CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE=InnoDB';
        }

        $this->createTable('{{%quick_fact}}', [
            'id' => $this->primaryKey(),
            'entity' => $this->string()->notNull(),
            'entity_id' => $this->integer()->notNull(),
            'title' => $this->string(255)->defaultValue(null),
            'h1' => $this->string(255)->defaultValue(null),
            'content' => $this->text()->notNull(),
            'source_type' => $this->tinyInteger()->notNull()->defaultValue(1),
            'status' => $this->tinyInteger()->notNull()->defaultValue(1),
            'published_at' => $this->dateTime()->defaultValue(null),
            'created_at' => $this->dateTime()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->dateTime()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        ], $options);

        // creates index for column `id`
        $this->createIndex(
            '{{%idx-quick_fact-quick_fact_entity}}',
            '{{%quick_fact}}',
            ['entity', 'entity_id']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropIndex(
            '{{%idx-quick_fact-quick_fact_entity}}',
            '{{%quick_fact}}'
        );

        $this->dropTable('{{%quick_fact}}');
    }
}
