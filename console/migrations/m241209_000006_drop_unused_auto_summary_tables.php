<?php

use yii\db\Migration;

/**
 * Class m241209_000006_drop_unused_auto_summary_tables
 */
class m241209_000006_drop_unused_auto_summary_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Drop auto_short_summary table as we're using short_summary column in content tables
        $this->dropTable('{{%auto_short_summary}}');
        
        // Drop auto_faq table as we're using the existing faq table
        $this->dropTable('{{%auto_faqs}}');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Recreate auto_short_summary table
        $this->createTable('{{%auto_short_summary}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'entity' => $this->string(50)->notNull(),
            'lang_code' => $this->string(10)->notNull(),
            'header' => $this->string(255)->null(),
            'paragraph_summary' => $this->text()->null(),
            'status' => $this->integer()->null(),
            'created_at' => $this->dateTime()->null(),
            'updated_at' => $this->dateTime()->null(),
        ]);

        // Recreate auto_faqs table
        $this->createTable('{{%auto_faqs}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'entity' => $this->string(50)->notNull(),
            'lang_code' => $this->string(10)->notNull(),
            'question' => $this->text()->notNull(),
            'answer' => $this->text()->notNull(),
            'sort_order' => $this->integer()->null(),
            'status' => $this->integer()->null(),
            'created_at' => $this->dateTime()->null(),
            'updated_at' => $this->dateTime()->null(),
        ]);
    }
}
