<?php

use yii\db\Migration;

/**
 * Add status column to summary tables that don't have it
 */
class m250107_140000_add_status_to_summary_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add status column to news_shorts (key content)
        $this->addColumn('{{%news_shorts}}', 'status', $this->tinyInteger(1)->notNull()->defaultValue(1)->comment('1=Active, 0=Inactive')->after('sort_order'));
        
        // Add status column to news_faqs
        $this->addColumn('{{%news_faqs}}', 'status', $this->tinyInteger(1)->notNull()->defaultValue(1)->comment('1=Active, 0=Inactive')->after('sort_order'));
        
        // Add status column to news_quiz
        $this->addColumn('{{%news_quiz}}', 'status', $this->tinyInteger(1)->notNull()->defaultValue(1)->comment('1=Active, 0=Inactive')->after('sort_order'));
        
        // Create indexes for status columns
        $this->createIndex('idx-news_shorts-status', '{{%news_shorts}}', 'status');
        $this->createIndex('idx-news_faqs-status', '{{%news_faqs}}', 'status');
        $this->createIndex('idx-news_quiz-status', '{{%news_quiz}}', 'status');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop indexes
        $this->dropIndex('idx-news_shorts-status', '{{%news_shorts}}');
        $this->dropIndex('idx-news_faqs-status', '{{%news_faqs}}');
        $this->dropIndex('idx-news_quiz-status', '{{%news_quiz}}');
        
        // Drop status columns
        $this->dropColumn('{{%news_shorts}}', 'status');
        $this->dropColumn('{{%news_faqs}}', 'status');
        $this->dropColumn('{{%news_quiz}}', 'status');
    }
}
