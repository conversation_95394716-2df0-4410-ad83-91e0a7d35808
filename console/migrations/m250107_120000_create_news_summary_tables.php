<?php

use yii\db\Migration;

/**
 * Class m250107_120000_create_news_summary_tables
 * Creates tables for storing auto-summary data components
 */
class m250107_120000_create_news_summary_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $tableOptions = null;
        if ($this->db->driverName === 'mysql') {
            $tableOptions = 'CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE=InnoDB';
        }

        // Table for header and paragraph summary
        $this->createTable('{{%news_shorts}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'header' => $this->string(255),
            'paragraph_summary' => $this->text(),
            'status' => $this->tinyInteger()->defaultValue(1),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
        ], $tableOptions);

        // Table for key content
        $this->createTable('{{%news_key_content}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'content' => $this->text()->notNull(),
            'sort_order' => $this->integer()->defaultValue(0),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
        ], $tableOptions);

        // Table for FAQs
        $this->createTable('{{%news_faqs}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'question' => $this->text()->notNull(),
            'answer' => $this->text()->notNull(),
            'sort_order' => $this->integer()->defaultValue(0),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
        ], $tableOptions);

        // Table for quiz
        $this->createTable('{{%news_quiz}}', [
            'id' => $this->primaryKey(),
            'entity_id' => $this->integer()->notNull(),
            'question' => $this->text()->notNull(),
            'options' => $this->json(),
            'correct_answer' => $this->string(255),
            'sort_order' => $this->integer()->defaultValue(0),
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
        ], $tableOptions);

        // Create indexes
        $this->createIndex('idx-news_shorts-entity_id', '{{%news_shorts}}', 'entity_id');
        $this->createIndex('idx-news_key_content-entity_id', '{{%news_key_content}}', 'entity_id');
        $this->createIndex('idx-news_faqs-entity_id', '{{%news_faqs}}', 'entity_id');
        $this->createIndex('idx-news_quiz-entity_id', '{{%news_quiz}}', 'entity_id');

        // Add foreign keys to news_subdomain table
        $this->addForeignKey(
            'fk-news_shorts-entity_id',
            '{{%news_shorts}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-news_key_content-entity_id',
            '{{%news_key_content}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-news_faqs-entity_id',
            '{{%news_faqs}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-news_quiz-entity_id',
            '{{%news_quiz}}',
            'entity_id',
            '{{%news_subdomain}}',
            'id',
            'CASCADE',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop foreign keys
        $this->dropForeignKey('fk-news_quiz-entity_id', '{{%news_quiz}}');
        $this->dropForeignKey('fk-news_faqs-entity_id', '{{%news_faqs}}');
        $this->dropForeignKey('fk-news_key_content-entity_id', '{{%news_key_content}}');
        $this->dropForeignKey('fk-news_shorts-entity_id', '{{%news_shorts}}');

        // Drop indexes
        $this->dropIndex('idx-news_quiz-entity_id', '{{%news_quiz}}');
        $this->dropIndex('idx-news_faqs-entity_id', '{{%news_faqs}}');
        $this->dropIndex('idx-news_key_content-entity_id', '{{%news_key_content}}');
        $this->dropIndex('idx-news_shorts-entity_id', '{{%news_shorts}}');

        // Drop tables
        $this->dropTable('{{%news_quiz}}');
        $this->dropTable('{{%news_faqs}}');
        $this->dropTable('{{%news_key_content}}');
        $this->dropTable('{{%news_shorts}}');
    }
}
