<?php

use yii\db\Migration;

/**
 * Add entity and lang_code columns and rename tables by removing 'news_' prefix
 */
class m250107_150000_add_entity_langcode_and_rename_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Add entity and lang_code columns to existing tables first
        
        // Add columns to news_short_summary
        $this->addColumn('{{%news_short_summary}}', 'entity', $this->string(50)->notNull()->comment('Entity type: news, article, boards')->after('entity_id'));
        $this->addColumn('{{%news_short_summary}}', 'lang_code', $this->string(10)->notNull()->defaultValue(1)->comment('Language code')->after('entity'));
        
        // Add columns to news_shorts
        $this->addColumn('{{%news_shorts}}', 'entity', $this->string(50)->notNull()->comment('Entity type: news, article, boards')->after('entity_id'));
        $this->addColumn('{{%news_shorts}}', 'lang_code', $this->string(10)->notNull()->defaultValue(1)->comment('Language code')->after('entity'));
        
        // Add columns to news_faqs
        $this->addColumn('{{%news_faqs}}', 'entity', $this->string(50)->notNull()->comment('Entity type: news, article, boards')->after('entity_id'));
        $this->addColumn('{{%news_faqs}}', 'lang_code', $this->string(10)->notNull()->defaultValue(1)->comment('Language code')->after('entity'));
        
        // Add columns to news_quiz
        $this->addColumn('{{%news_quiz}}', 'entity', $this->string(50)->notNull()->comment('Entity type: news, article, boards')->after('entity_id'));
        $this->addColumn('{{%news_quiz}}', 'lang_code', $this->string(10)->notNull()->defaultValue(1)->comment('Language code')->after('entity'));
        
        // Create indexes for entity and lang_code columns
        $this->createIndex('idx-news_short_summary-entity', '{{%news_short_summary}}', 'entity');
        $this->createIndex('idx-news_short_summary-lang_code', '{{%news_short_summary}}', 'lang_code');
        $this->createIndex('idx-news_short_summary-entity-lang', '{{%news_short_summary}}', ['entity', 'lang_code']);
        
        $this->createIndex('idx-news_shorts-entity', '{{%news_shorts}}', 'entity');
        $this->createIndex('idx-news_shorts-lang_code', '{{%news_shorts}}', 'lang_code');
        $this->createIndex('idx-news_shorts-entity-lang', '{{%news_shorts}}', ['entity', 'lang_code']);
        
        $this->createIndex('idx-news_faqs-entity', '{{%news_faqs}}', 'entity');
        $this->createIndex('idx-news_faqs-lang_code', '{{%news_faqs}}', 'lang_code');
        $this->createIndex('idx-news_faqs-entity-lang', '{{%news_faqs}}', ['entity', 'lang_code']);
        
        $this->createIndex('idx-news_quiz-entity', '{{%news_quiz}}', 'entity');
        $this->createIndex('idx-news_quiz-lang_code', '{{%news_quiz}}', 'lang_code');
        $this->createIndex('idx-news_quiz-entity-lang', '{{%news_quiz}}', ['entity', 'lang_code']);
        
        // Now rename the tables (remove 'news_' prefix)
        
        // Drop foreign keys first
        $this->dropForeignKey('fk-news_short_summary-entity_id', '{{%news_short_summary}}');
        $this->dropForeignKey('fk-news_shorts-entity_id', '{{%news_shorts}}');
        $this->dropForeignKey('fk-news_faqs-entity_id', '{{%news_faqs}}');
        $this->dropForeignKey('fk-news_quiz-entity_id', '{{%news_quiz}}');
        
        // Rename tables
        $this->renameTable('{{%news_short_summary}}', '{{%auto_short_summary}}');
        $this->renameTable('{{%news_shorts}}', '{{%auto_shorts}}');
        $this->renameTable('{{%news_faqs}}', '{{%auto_faqs}}');
        $this->renameTable('{{%news_quiz}}', '{{%auto_quiz}}');
        
        // Recreate foreign keys with new table names
        $this->addForeignKey('fk-auto_short_summary-entity_id', '{{%auto_short_summary}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-auto_shorts-entity_id', '{{%auto_shorts}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-auto_faqs-entity_id', '{{%auto_faqs}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-auto_quiz-entity_id', '{{%auto_quiz}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Drop foreign keys
        $this->dropForeignKey('fk-auto_short_summary-entity_id', '{{%auto_short_summary}}');
        $this->dropForeignKey('fk-auto_shorts-entity_id', '{{%auto_shorts}}');
        $this->dropForeignKey('fk-auto_faqs-entity_id', '{{%auto_faqs}}');
        $this->dropForeignKey('fk-auto_quiz-entity_id', '{{%auto_quiz}}');
        
        // Rename tables back
        $this->renameTable('{{%auto_short_summary}}', '{{%news_short_summary}}');
        $this->renameTable('{{%auto_shorts}}', '{{%news_shorts}}');
        $this->renameTable('{{%auto_faqs}}', '{{%news_faqs}}');
        $this->renameTable('{{%auto_quiz}}', '{{%news_quiz}}');
        
        // Drop indexes
        $this->dropIndex('idx-news_short_summary-entity-lang', '{{%news_short_summary}}');
        $this->dropIndex('idx-news_short_summary-lang_code', '{{%news_short_summary}}');
        $this->dropIndex('idx-news_short_summary-entity', '{{%news_short_summary}}');
        
        $this->dropIndex('idx-news_shorts-entity-lang', '{{%news_shorts}}');
        $this->dropIndex('idx-news_shorts-lang_code', '{{%news_shorts}}');
        $this->dropIndex('idx-news_shorts-entity', '{{%news_shorts}}');
        
        $this->dropIndex('idx-news_faqs-entity-lang', '{{%news_faqs}}');
        $this->dropIndex('idx-news_faqs-lang_code', '{{%news_faqs}}');
        $this->dropIndex('idx-news_faqs-entity', '{{%news_faqs}}');
        
        $this->dropIndex('idx-news_quiz-entity-lang', '{{%news_quiz}}');
        $this->dropIndex('idx-news_quiz-lang_code', '{{%news_quiz}}');
        $this->dropIndex('idx-news_quiz-entity', '{{%news_quiz}}');
        
        // Drop entity and lang_code columns
        $this->dropColumn('{{%news_short_summary}}', 'lang_code');
        $this->dropColumn('{{%news_short_summary}}', 'entity');
        $this->dropColumn('{{%news_shorts}}', 'lang_code');
        $this->dropColumn('{{%news_shorts}}', 'entity');
        $this->dropColumn('{{%news_faqs}}', 'lang_code');
        $this->dropColumn('{{%news_faqs}}', 'entity');
        $this->dropColumn('{{%news_quiz}}', 'lang_code');
        $this->dropColumn('{{%news_quiz}}', 'entity');
        
        // Recreate original foreign keys
        $this->addForeignKey('fk-news_short_summary-entity_id', '{{%news_short_summary}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-news_shorts-entity_id', '{{%news_shorts}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-news_faqs-entity_id', '{{%news_faqs}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
        $this->addForeignKey('fk-news_quiz-entity_id', '{{%news_quiz}}', 'entity_id', '{{%news_subdomain}}', 'id', 'CASCADE', 'CASCADE');
    }
}
